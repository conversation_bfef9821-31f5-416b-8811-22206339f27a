-- =====================================================
-- Script de test pour les requêtes MySQL Stablecoins EUR
-- =====================================================

-- Vérification de l'installation
SELECT 'Installation vérifiée' as status;
SHOW TABLES LIKE 'stablecoins%';

-- =====================================================
-- REQUÊTES DE BASE
-- =====================================================

-- 1. Aperçu général des données
SELECT 
    'Données générales' as section,
    COUNT(*) as total_stablecoins,
    COUNT(CASE WHEN capitalisation_usd > 0 THEN 1 END) as avec_capitalisation,
    COUNT(CASE WHEN conformite_mica = 'X' THEN 1 END) as conformes_mica
FROM stablecoins_eur;

-- 2. Top 5 stablecoins par capitalisation
SELECT 
    'Top 5 Capitalisation' as section,
    nom, 
    emetteur, 
    CONCAT('$', FORMAT(capitalisation_usd, 2)) as capitalisation,
    CONCAT(ROUND(volume_hebdo_cap_ratio, 2), '%') as ratio_volume
FROM stablecoins_eur 
WHERE capitalisation_usd > 0
ORDER BY capitalisation_usd DESC 
LIMIT 5;

-- 3. Stablecoins les plus actifs (volume/cap ratio)
SELECT 
    'Plus Actifs' as section,
    nom, 
    emetteur,
    CONCAT('$', FORMAT(capitalisation_usd, 2)) as capitalisation,
    CONCAT(ROUND(volume_hebdo_cap_ratio, 2), '%') as ratio_volume
FROM stablecoins_eur 
WHERE capitalisation_usd > 1000000  -- Au moins 1M$ de cap
ORDER BY volume_hebdo_cap_ratio DESC 
LIMIT 5;

-- =====================================================
-- ANALYSES AVANCÉES
-- =====================================================

-- 4. Répartition par émetteur
SELECT 
    'Répartition Émetteurs' as section,
    emetteur,
    COUNT(*) as nb_tokens,
    CONCAT('$', FORMAT(SUM(capitalisation_usd), 2)) as cap_totale,
    CONCAT('$', FORMAT(AVG(capitalisation_usd), 2)) as cap_moyenne,
    ROUND(AVG(prix_usd), 4) as prix_moyen
FROM stablecoins_eur
GROUP BY emetteur
HAVING SUM(capitalisation_usd) > 0
ORDER BY SUM(capitalisation_usd) DESC;

-- 5. Analyse de la conformité MiCA
SELECT 
    'Conformité MiCA' as section,
    conformite_mica,
    COUNT(*) as nombre,
    CONCAT(ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM stablecoins_eur), 1), '%') as pourcentage,
    CONCAT('$', FORMAT(SUM(capitalisation_usd), 2)) as capitalisation_totale
FROM stablecoins_eur
GROUP BY conformite_mica
ORDER BY SUM(capitalisation_usd) DESC;

-- 6. Distribution des capitalisations
SELECT 
    'Distribution Capitalisation' as section,
    CASE 
        WHEN capitalisation_usd = 0 THEN 'Aucune donnée'
        WHEN capitalisation_usd < 1000000 THEN '< 1M$'
        WHEN capitalisation_usd < 10000000 THEN '1M$ - 10M$'
        WHEN capitalisation_usd < 100000000 THEN '10M$ - 100M$'
        ELSE '> 100M$'
    END as tranche_capitalisation,
    COUNT(*) as nombre_stablecoins,
    CONCAT('$', FORMAT(SUM(capitalisation_usd), 2)) as capitalisation_totale
FROM stablecoins_eur
GROUP BY 
    CASE 
        WHEN capitalisation_usd = 0 THEN 'Aucune donnée'
        WHEN capitalisation_usd < 1000000 THEN '< 1M$'
        WHEN capitalisation_usd < 10000000 THEN '1M$ - 10M$'
        WHEN capitalisation_usd < 100000000 THEN '10M$ - 100M$'
        ELSE '> 100M$'
    END
ORDER BY MIN(capitalisation_usd);

-- =====================================================
-- REQUÊTES DE MONITORING
-- =====================================================

-- 7. Stablecoins avec prix anormal (trop éloigné de 1 USD)
SELECT 
    'Prix Anormaux' as section,
    nom,
    emetteur,
    prix_usd,
    ABS(prix_usd - 1.0) as ecart_dollar,
    CASE 
        WHEN prix_usd = 0 THEN 'Pas de données'
        WHEN ABS(prix_usd - 1.0) > 0.05 THEN 'Écart important'
        WHEN ABS(prix_usd - 1.0) > 0.02 THEN 'Écart modéré'
        ELSE 'Normal'
    END as statut_prix
FROM stablecoins_eur
WHERE prix_usd > 0
ORDER BY ABS(prix_usd - 1.0) DESC;

-- 8. Tokens sans données de capitalisation
SELECT 
    'Sans Capitalisation' as section,
    nom,
    emetteur,
    prix_usd,
    volume_24h_usd
FROM stablecoins_eur
WHERE capitalisation_usd = 0
ORDER BY volume_24h_usd DESC;

-- =====================================================
-- STATISTIQUES GLOBALES
-- =====================================================

-- 9. Résumé complet du marché
SELECT 
    'Résumé Marché' as section,
    COUNT(*) as total_stablecoins,
    COUNT(CASE WHEN capitalisation_usd > 0 THEN 1 END) as avec_donnees,
    CONCAT('$', FORMAT(SUM(capitalisation_usd), 2)) as capitalisation_totale,
    CONCAT('$', FORMAT(AVG(CASE WHEN capitalisation_usd > 0 THEN capitalisation_usd END), 2)) as cap_moyenne,
    CONCAT('$', FORMAT(SUM(volume_24h_usd), 2)) as volume_24h_total,
    ROUND(AVG(CASE WHEN capitalisation_usd > 0 THEN volume_hebdo_cap_ratio END), 2) as ratio_moyen,
    MIN(CASE WHEN prix_usd > 0 THEN prix_usd END) as prix_min,
    MAX(prix_usd) as prix_max
FROM stablecoins_eur;

-- 10. Comparaison des 3 plus gros émetteurs
SELECT 
    'Top 3 Émetteurs' as section,
    emetteur,
    GROUP_CONCAT(nom ORDER BY capitalisation_usd DESC) as tokens,
    COUNT(*) as nb_tokens,
    CONCAT('$', FORMAT(SUM(capitalisation_usd), 2)) as capitalisation_totale,
    CONCAT('$', FORMAT(SUM(volume_24h_usd), 2)) as volume_24h_total
FROM stablecoins_eur
WHERE capitalisation_usd > 0
GROUP BY emetteur
ORDER BY SUM(capitalisation_usd) DESC
LIMIT 3;

-- =====================================================
-- REQUÊTES POUR TABLEAUX DE BORD
-- =====================================================

-- 11. Données pour graphique en secteurs (capitalisation)
SELECT 
    CASE 
        WHEN capitalisation_usd > 50000000 THEN nom
        ELSE 'Autres'
    END as label,
    SUM(capitalisation_usd) as valeur
FROM stablecoins_eur
WHERE capitalisation_usd > 0
GROUP BY 
    CASE 
        WHEN capitalisation_usd > 50000000 THEN nom
        ELSE 'Autres'
    END
ORDER BY valeur DESC;

-- 12. Données pour graphique en barres (volume/cap ratio)
SELECT 
    nom as label,
    volume_hebdo_cap_ratio as valeur
FROM stablecoins_eur
WHERE capitalisation_usd > 1000000
ORDER BY volume_hebdo_cap_ratio DESC
LIMIT 10;

-- =====================================================
-- VÉRIFICATIONS DE QUALITÉ DES DONNÉES
-- =====================================================

-- 13. Cohérence des données
SELECT 
    'Vérifications' as section,
    SUM(CASE WHEN prix_usd < 0 THEN 1 ELSE 0 END) as prix_negatifs,
    SUM(CASE WHEN capitalisation_usd < 0 THEN 1 ELSE 0 END) as cap_negatives,
    SUM(CASE WHEN offre_circulation < 0 THEN 1 ELSE 0 END) as offre_negative,
    SUM(CASE WHEN volume_24h_usd < 0 THEN 1 ELSE 0 END) as volume_negatif,
    SUM(CASE WHEN prix_usd > 0 AND capitalisation_usd = 0 THEN 1 ELSE 0 END) as prix_sans_cap,
    SUM(CASE WHEN capitalisation_usd > 0 AND prix_usd = 0 THEN 1 ELSE 0 END) as cap_sans_prix
FROM stablecoins_eur;

-- =====================================================
-- EXPORT POUR ANALYSE EXTERNE
-- =====================================================

-- 14. Export CSV-ready (décommentez pour utiliser)
/*
SELECT 
    nom,
    emetteur,
    conformite_mica,
    prix_usd,
    capitalisation_usd,
    offre_circulation,
    volume_24h_usd,
    volume_hebdo_usd,
    volume_hebdo_cap_ratio,
    date_maj
FROM stablecoins_eur
ORDER BY capitalisation_usd DESC
INTO OUTFILE '/tmp/stablecoins_export.csv'
FIELDS TERMINATED BY ','
ENCLOSED BY '"'
LINES TERMINATED BY '\n';
*/

-- Fin du script de test
SELECT 'Tests terminés avec succès!' as message;
