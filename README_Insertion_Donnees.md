# Guide d'insertion des données Stablecoins EUR dans MySQL

## 📋 Vue d'ensemble

Ce guide explique comment récupérer les données de stablecoins EUR depuis l'API CoinMarketCap et les insérer dans votre base de données MySQL en utilisant le script Python modifié.

## 🔄 Processus complet de récupération et insertion

### 1. Récupération automatique des données

Le script `scrapeurohebdo.py` récupère automatiquement les données depuis l'API CoinMarketCap :

```python
# Le script récupère ces données pour chaque stablecoin :
- Nom/Symbole (ex: EURC, EURS)
- Prix en USD
- Capitalisation de marché
- Offre en circulation
- Volume 24h
- Émetteur (défini dans le script)
- Conformité MiCA (définie dans le script)
```

### 2. Génération automatique du fichier SQL

Quand vous exécutez le script :

```bash
python3 scrapeurohebdo.py
```

Le script génère automatiquement :
- ✅ `stablecoins_eur_data.sql` - Fichier SQL complet
- ✅ Structure des tables MySQL
- ✅ Instructions INSERT avec toutes les données
- ✅ Requêtes d'exemple

## 🗄️ Installation et insertion en base

### Étape 1 : Préparer MySQL

```sql
-- 1. Créer la base de données
CREATE DATABASE stablecoins_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 2. Sélectionner la base
USE stablecoins_db;

-- 3. Vérifier que vous êtes dans la bonne base
SELECT DATABASE();
```

### Étape 2 : Importer le fichier SQL généré

**Option A : Via ligne de commande MySQL**
```bash
mysql -u votre_utilisateur -p stablecoins_db < stablecoins_eur_data.sql
```

**Option B : Via client MySQL**
```sql
SOURCE /chemin/complet/vers/stablecoins_eur_data.sql;
```

**Option C : Via phpMyAdmin**
1. Sélectionnez la base `stablecoins_db`
2. Onglet "Importer"
3. Choisir le fichier `stablecoins_eur_data.sql`
4. Cliquer "Exécuter"

### Étape 3 : Vérifier l'insertion

```sql
-- Vérifier que les tables sont créées
SHOW TABLES;

-- Compter les enregistrements
SELECT COUNT(*) as total_stablecoins FROM stablecoins_eur;

-- Voir les premières données
SELECT nom, emetteur, capitalisation_usd 
FROM stablecoins_eur 
ORDER BY capitalisation_usd DESC 
LIMIT 5;
```

## 🔄 Mise à jour automatique des données

### Méthode 1 : Remplacement complet (recommandé)

Le fichier SQL généré contient `DROP TABLE IF EXISTS` qui supprime et recrée les tables avec les nouvelles données :

```bash
# Exécuter le script Python pour récupérer les nouvelles données
python3 scrapeurohebdo.py

# Importer le nouveau fichier SQL (remplace toutes les données)
mysql -u utilisateur -p stablecoins_db < stablecoins_eur_data.sql
```

### Méthode 2 : Mise à jour avec historique

Pour conserver l'historique, utilisez la procédure stockée incluse :

```sql
-- 1. D'abord, importer les nouvelles données dans la table principale
-- (en utilisant la méthode 1 ci-dessus)

-- 2. Ensuite, sauvegarder dans l'historique
CALL InsererHistorique();

-- 3. Vérifier l'historique
SELECT COUNT(*) FROM stablecoins_eur_historique;
```

## 📊 Structure des données insérées

### Table principale : `stablecoins_eur`

```sql
-- Exemple de données insérées :
INSERT INTO stablecoins_eur (
    nom, emetteur, conformite_mica, prix_usd, capitalisation_usd,
    offre_circulation, volume_24h_usd, volume_hebdo_usd, volume_hebdo_cap_ratio
) VALUES
    ('EURC', 'Circle', 'X', 1.1687, 209444958.63, 179206805.57, 41719782.87, 292038480.1, 139.43),
    ('EURS', 'Stasis', 'X', 1.1742, 145752341.95, 124125940.0, 5.38, 37.64, 0.0),
    -- ... autres stablecoins
```

### Colonnes expliquées :

| Colonne | Type | Description | Exemple |
|---------|------|-------------|---------|
| `nom` | VARCHAR(20) | Symbole du stablecoin | 'EURC' |
| `emetteur` | VARCHAR(100) | Société émettrice | 'Circle' |
| `conformite_mica` | VARCHAR(20) | Statut MiCA | 'X' |
| `prix_usd` | DECIMAL(10,6) | Prix en USD | 1.1687 |
| `capitalisation_usd` | DECIMAL(20,2) | Cap. de marché | 209444958.63 |
| `offre_circulation` | DECIMAL(20,2) | Tokens en circulation | 179206805.57 |
| `volume_24h_usd` | DECIMAL(20,2) | Volume 24h | 41719782.87 |
| `volume_hebdo_usd` | DECIMAL(20,2) | Volume hebdo estimé | 292038480.1 |
| `volume_hebdo_cap_ratio` | DECIMAL(8,4) | Ratio volume/cap (%) | 139.43 |

## 🔧 Automatisation avec Cron

### Script de mise à jour quotidienne

Créez un script `update_stablecoins.sh` :

```bash
#!/bin/bash
cd /chemin/vers/votre/script
python3 scrapeurohebdo.py
mysql -u utilisateur -p'motdepasse' stablecoins_db < stablecoins_eur_data.sql
mysql -u utilisateur -p'motdepasse' stablecoins_db -e "CALL InsererHistorique();"
echo "Mise à jour terminée : $(date)"
```

### Crontab pour exécution automatique

```bash
# Éditer le crontab
crontab -e

# Ajouter cette ligne pour exécution quotidienne à 9h00
0 9 * * * /chemin/vers/update_stablecoins.sh >> /var/log/stablecoins_update.log 2>&1
```

## 🔍 Vérification et monitoring

### Requêtes de vérification

```sql
-- 1. Vérifier la fraîcheur des données
SELECT 
    MAX(date_maj) as derniere_maj,
    COUNT(*) as nb_stablecoins,
    SUM(capitalisation_usd) as cap_totale
FROM stablecoins_eur;

-- 2. Détecter les données manquantes
SELECT nom, emetteur 
FROM stablecoins_eur 
WHERE capitalisation_usd = 0 OR prix_usd = 0;

-- 3. Évolution dans l'historique
SELECT 
    date_snapshot,
    COUNT(*) as nb_stablecoins,
    SUM(capitalisation_usd) as cap_totale
FROM stablecoins_eur_historique
GROUP BY date_snapshot
ORDER BY date_snapshot DESC
LIMIT 7;
```

### Alertes automatiques

```sql
-- Créer une vue pour les alertes
CREATE VIEW alertes_stablecoins AS
SELECT 
    nom,
    emetteur,
    CASE 
        WHEN prix_usd = 0 THEN 'Prix manquant'
        WHEN capitalisation_usd = 0 THEN 'Capitalisation manquante'
        WHEN ABS(prix_usd - 1.0) > 0.05 THEN 'Prix anormal'
        ELSE 'OK'
    END as alerte
FROM stablecoins_eur
WHERE prix_usd = 0 OR capitalisation_usd = 0 OR ABS(prix_usd - 1.0) > 0.05;

-- Consulter les alertes
SELECT * FROM alertes_stablecoins WHERE alerte != 'OK';
```

## 🚨 Dépannage

### Erreurs courantes

**1. Erreur de connexion MySQL**
```bash
ERROR 1045 (28000): Access denied for user
```
**Solution :** Vérifiez vos identifiants MySQL

**2. Base de données inexistante**
```bash
ERROR 1049 (42000): Unknown database 'stablecoins_db'
```
**Solution :** Créez d'abord la base avec `CREATE DATABASE stablecoins_db;`

**3. Fichier SQL introuvable**
```bash
ERROR: File not found
```
**Solution :** Vérifiez le chemin complet vers `stablecoins_eur_data.sql`

**4. Données dupliquées**
```bash
ERROR 1062 (23000): Duplicate entry
```
**Solution :** Le script utilise `DROP TABLE IF EXISTS` pour éviter ce problème

### Logs et debugging

```sql
-- Activer les logs MySQL pour debugging
SET GLOBAL general_log = 'ON';
SET GLOBAL log_output = 'TABLE';

-- Consulter les logs
SELECT * FROM mysql.general_log 
WHERE command_type = 'Query' 
ORDER BY event_time DESC 
LIMIT 10;
```

## 📈 Utilisation des données

Une fois les données insérées, vous pouvez :

✅ **Créer des tableaux de bord** avec les requêtes fournies  
✅ **Analyser les tendances** avec la table d'historique  
✅ **Intégrer avec des outils BI** (Power BI, Tableau, etc.)  
✅ **Créer des API** pour exposer les données  
✅ **Automatiser des rapports** avec les procédures stockées  

---

**Note :** Ce processus récupère et insère automatiquement toutes les données disponibles depuis l'API CoinMarketCap pour les stablecoins EUR configurés dans le script.
