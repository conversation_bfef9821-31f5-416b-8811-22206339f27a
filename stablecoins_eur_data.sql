-- =====================================================
-- Export des données Stablecoins EUR vers MySQL
-- Généré le : 2025-07-28 11:06:01
-- =====================================================

DROP TABLE IF EXISTS stablecoins_eur;

CREATE TABLE stablecoins_eur (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(20) NOT NULL,
    emetteur VARCHAR(100) NOT NULL,
    conformite_mica VARCHAR(20) DEFAULT 'À vérifier',
    prix_usd DECIMAL(10, 6) DEFAULT 0.0,
    capitalisation_usd DECIMAL(20, 2) DEFAULT 0.0,
    offre_circulation DECIMAL(20, 2) DEFAULT 0.0,
    volume_24h_usd DECIMAL(20, 2) DEFAULT 0.0,
    volume_hebdo_usd DECIMAL(20, 2) DEFAULT 0.0,
    volume_hebdo_cap_ratio DECIMAL(8, 4) DEFAULT 0.0,
    date_maj TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_nom (nom),
    INDEX idx_emetteur (emetteur),
    INDEX idx_capitalisation (capitalisation_usd),
    INDEX idx_date_maj (date_maj)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insertion des données
INSERT INTO stablecoins_eur (
    nom, emetteur, conformite_mica, prix_usd, capitalisation_usd,
    offre_circulation, volume_24h_usd, volume_hebdo_usd, volume_hebdo_cap_ratio
) VALUES
    ('EURC', 'Circle', 'X', 1.1687, *********.63, *********.57, ********.87, *********.1, 139.43),
    ('EURS', 'Stasis', 'X', 1.1742, *********.95, *********.0, 5.38, 37.64, 0.0),
    ('EURI', 'Banking Circle', 'X', 1.1681, ********.7, ********.81, 9663170.17, ********.22, 114.44),
    ('AEUR', 'Anchored Coins', 'X', 1.1536, ********.38, ********.6, 16641.57, 116491.02, 0.21),
    ('EURCV', 'Société Générale-FORGE', 'X', 1.1738, ********.96, ********.0, ********.66, ********.65, 158.39),
    ('EURt', 'Tether', 'X', 1.1524, ********.33, ********.03, 391498.69, 2740490.84, 6.54),
    ('EURA', 'Angle Protocol', 'X', 1.1704, ********.79, ********.0, 4458.82, 31211.75, 0.1),
    ('EURR', 'StablR', 'X', 1.1665, ********.61, ********.63, 4320394.47, ********.32, 235.19),
    ('EURQ', 'Quantoz', 'X', 1.1675, 3911532.95, 3350265.0, 3343004.9, ********.28, 598.26),
    ('CEUR', 'Celo Foundation', 'X', 1.1702, 3705915.01, 3167024.98, 492643.63, 3448505.43, 93.05),
    ('VEUR', 'VNX', 'X', 1.1673, 3248674.65, 2783082.75, 582312.01, 4076184.1, 125.47),
    ('DEURO', 'Decentralized Euros', 'X', 1.2239, 2652223.63, 2166970.85, 282623.94, 1978367.57, 74.59),
    ('EUROP', 'Schuman', 'X', 1.1685, 2493603.91, 2133961.0, 28101.44, 196710.1, 7.89),
    ('sEUR', 'Synthetix / Jarvis', 'X', 0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
    ('EUROE', 'Membrane Finance', 'X', 1.1699, 0.0, 0.0, 7520.76, 52645.31, 0.0),
    ('EEUR', 'e-Money', 'X', 0.2874, 0.0, 0.0, 0.0, 0.0, 0.0),
    ('EURL', 'Lugh / Casino Group', 'X', 0.0, 0.0, 0.0, 0.0, 0.0, 0.0),
    ('PAR', 'Mimo Capital', 'X', 1.0008, 0.0, 0.0, 1.35, 9.45, 0.0);

-- =====================================================
-- Requêtes d'exemple utiles
-- =====================================================

-- Top 10 par capitalisation
SELECT nom, emetteur, capitalisation_usd, conformite_mica
FROM stablecoins_eur
ORDER BY capitalisation_usd DESC
LIMIT 10;

-- Stablecoins conformes MiCA
SELECT nom, emetteur, capitalisation_usd
FROM stablecoins_eur
WHERE conformite_mica = 'X'
ORDER BY capitalisation_usd DESC;

-- Statistiques globales
SELECT 
    COUNT(*) as nombre_stablecoins,
    SUM(capitalisation_usd) as capitalisation_totale,
    AVG(capitalisation_usd) as capitalisation_moyenne,
    SUM(volume_24h_usd) as volume_24h_total,
    AVG(volume_hebdo_cap_ratio) as ratio_moyen
FROM stablecoins_eur;

-- Répartition par émetteur
SELECT 
    emetteur,
    COUNT(*) as nombre_tokens,
    SUM(capitalisation_usd) as capitalisation_totale,
    AVG(prix_usd) as prix_moyen
FROM stablecoins_eur
GROUP BY emetteur
ORDER BY capitalisation_totale DESC;

-- Tokens avec le plus fort ratio volume/capitalisation
SELECT nom, emetteur, volume_hebdo_cap_ratio, capitalisation_usd
FROM stablecoins_eur
WHERE capitalisation_usd > 0
ORDER BY volume_hebdo_cap_ratio DESC
LIMIT 10;

-- =====================================================
-- Table d'historique pour le suivi temporel
-- =====================================================

DROP TABLE IF EXISTS stablecoins_eur_historique;

CREATE TABLE stablecoins_eur_historique (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(20) NOT NULL,
    emetteur VARCHAR(100) NOT NULL,
    conformite_mica VARCHAR(20) DEFAULT 'À vérifier',
    prix_usd DECIMAL(10, 6) DEFAULT 0.0,
    capitalisation_usd DECIMAL(20, 2) DEFAULT 0.0,
    offre_circulation DECIMAL(20, 2) DEFAULT 0.0,
    volume_24h_usd DECIMAL(20, 2) DEFAULT 0.0,
    volume_hebdo_usd DECIMAL(20, 2) DEFAULT 0.0,
    volume_hebdo_cap_ratio DECIMAL(8, 4) DEFAULT 0.0,
    date_snapshot DATE NOT NULL,
    heure_snapshot TIME NOT NULL,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_nom_date (nom, date_snapshot),
    INDEX idx_date_snapshot (date_snapshot),
    INDEX idx_capitalisation_hist (capitalisation_usd),
    UNIQUE KEY unique_nom_date (nom, date_snapshot)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Procédure pour insérer les données actuelles dans l'historique
DELIMITER //
CREATE PROCEDURE InsererHistorique()
BEGIN
    INSERT INTO stablecoins_eur_historique (
        nom, emetteur, conformite_mica, prix_usd, capitalisation_usd,
        offre_circulation, volume_24h_usd, volume_hebdo_usd, volume_hebdo_cap_ratio,
        date_snapshot, heure_snapshot
    )
    SELECT 
        nom, emetteur, conformite_mica, prix_usd, capitalisation_usd,
        offre_circulation, volume_24h_usd, volume_hebdo_usd, volume_hebdo_cap_ratio,
        CURDATE(), CURTIME()
    FROM stablecoins_eur
    ON DUPLICATE KEY UPDATE
        prix_usd = VALUES(prix_usd),
        capitalisation_usd = VALUES(capitalisation_usd),
        offre_circulation = VALUES(offre_circulation),
        volume_24h_usd = VALUES(volume_24h_usd),
        volume_hebdo_usd = VALUES(volume_hebdo_usd),
        volume_hebdo_cap_ratio = VALUES(volume_hebdo_cap_ratio),
        heure_snapshot = VALUES(heure_snapshot);
END //
DELIMITER ;

-- Requêtes d'analyse historique
-- Évolution de la capitalisation sur les 30 derniers jours
/*
SELECT 
    nom,
    date_snapshot,
    capitalisation_usd,
    LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot) as cap_precedente,
    ROUND(((capitalisation_usd - LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot)) / LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot)) * 100, 2) as variation_pct
FROM stablecoins_eur_historique
WHERE date_snapshot >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
ORDER BY nom, date_snapshot;
*/

