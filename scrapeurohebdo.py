import requests
import pandas as pd
import os
import platform
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
import subprocess
from typing import List, Dict, Any, Optional
import json

# --- CONFIGURATION ---
API_KEY = os.getenv("CMC_API_KEY", "0e53eb7b-4d61-4604-9923-9dc343a96b74")

if API_KEY == "0e53eb7b-4d61-4604-9923-9dc343a96b74":
    print("⚠️  Attention : Vous utilisez une clé API par défaut. Pensez à la remplacer par votre propre clé.")
    print("⚠️  Pour des raisons de sécurité, il est recommandé de définir la clé API via une variable d'environnement (CMC_API_KEY).\n")

SYMBOLS: List[str] = [
    "EURC", "EURS", "EURt", "EURA", "PAR", "EURL", "AEUR",
    "CEUR", "EURR", "EURQ", "VEUR", "DEURO", "sEUR", "EUROE", "EEUR",
    "EURI", "EURCV", "EUROP"  # Ajout de EUROP
]

EMETTEURS: Dict[str, str] = {
    "EURC": "Circle", "EURS": "Stasis", "EURt": "Tether", "EURA": "Angle Protocol",
    "PAR": "Mimo Capital", "EURL": "Lugh / Casino Group", "EURe": "Monerium",
    "AEUR": "Anchored Coins", "CEUR": "Celo Foundation", "EURR": "StablR",
    "EURQ": "Quantoz", "VEUR": "VNX", "DEURO": "Decentralized Euros",
    "sEUR": "Synthetix / Jarvis", "EUROE": "Membrane Finance", "EEUR": "e-Money",
    "EURI": "Banking Circle", "EURCV": "Société Générale-FORGE", "EUROP": "Schuman"  # Modification de l'émetteur
}

MICA_STATUS: Dict[str, str] = {
    "EURC": "X",
    "EURS": "X",
    "EURt": "X",
    "EURA": "X",
    "PAR": "X",
    "EURL": "X",
    "EURe": "X",
    "AEUR": "X",
    "CEUR": "X",
    "EURR": "X",
    "EURQ": "X",
    "VEUR": "X",
    "DEURO": "X",
    "sEUR": "X",
    "EUROE": "X",
    "EEUR": "X",
    "EURI": "X",
    "EURCV": "X",
    "EUROP": "X"  # Ajout du statut MiCA
}

COULEURS: List[str] = [
    '#4E79A7', '#F28E2B', '#E15759', '#76B7B2', '#59A14F', '#EDC948',
    '#B07AA1', '#FF9DA7', '#9C755F', '#BAB0AC', '#A0CBE8', '#FFBE7D',
    '#F4B3B3', '#BDE0DA', '#A2D99E', '#FCE29A', '#D3B6CD', '#FFCFD2',
    '#CBBBAF', '#DCD6D4'
]

EXCEL_FILENAME = "Rapport_Stablecoins_EUR.xlsx"
PDF_FILENAME = "Rapport_Stablecoins_EUR.pdf"
SQL_FILENAME = "stablecoins_eur_data.sql"
# HISTORY_FILENAME is removed as history is no longer included

# Define sheet names for the desired PDF page order
CHARTS_COMBINED_SHEET_NAME = "Graphiques" # Single sheet for both charts
DATA_TABLE_SHEET_NAME = "Tableau Données"


# --- FONCTIONS UTILITAIRES ---
def fetch_coin_data(api_key: str, symbols_list: List[str]) -> Optional[Dict[str, Any]]:
    """Récupère les données des cryptomonnaies depuis l'API CoinMarketCap."""
    headers = {
        'Accepts': 'application/json',
        'X-CMC_PRO_API_KEY': api_key,
    }
    params = {
        'symbol': ','.join(symbols_list),
        'convert': 'USD'
    }
    url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"

    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if 'data' not in data or not data['data']:
            error_message = data.get('status', {}).get('error_message', 'Réponse API invalide ou vide.')
            print(f"❌ Erreur API : {error_message}")
            if data.get('status', {}).get('error_code') == 1001:
                 print("❌ Vérifiez votre clé API CoinMarketCap (X-CMC_PRO_API_KEY).")
            return None
        return data
    except requests.exceptions.HTTPError as http_err:
        print(f"❌ Erreur HTTP : {http_err} - Contenu : {response.text}")
    except requests.exceptions.ConnectionError as conn_err:
        print(f"❌ Erreur de Connexion : {conn_err}")
    except requests.exceptions.Timeout as timeout_err:
        print(f"❌ Erreur de Timeout : {timeout_err}")
    except requests.exceptions.RequestException as req_err:
        print(f"❌ Erreur Requête API : {req_err}")
    except ValueError as json_err:
        print(f"❌ Erreur de décodage JSON : {json_err} - Réponse reçue: {response.text}")
    return None

# Données de capitalisation alternatives pour les tokens qui pourraient manquer dans l'API
MARKET_CAP_FALLBACKS = {
    "EUROP": 2450000,  # 2.45M pour EUROP
    # Ajoutez d'autres tokens si nécessaire
}

# Chemin pour stocker les données de capitalisation récupérées
MARKET_CAP_CACHE_FILE = "market_cap_cache.json"

def load_cached_market_caps():
    """Charge les données de capitalisation mises en cache."""
    try:
        if os.path.exists(MARKET_CAP_CACHE_FILE):
            with open(MARKET_CAP_CACHE_FILE, 'r') as f:
                data = json.load(f)
                # Vérifier si les données sont récentes (moins de 24h)
                if datetime.now() - datetime.fromisoformat(data.get('timestamp', '2000-01-01')) < timedelta(hours=24):
                    return data.get('market_caps', {})
    except Exception as e:
        print(f"⚠️ Erreur lors du chargement du cache de capitalisation: {e}")
    return {}

def save_cached_market_caps(market_caps):
    """Sauvegarde les données de capitalisation en cache."""
    try:
        with open(MARKET_CAP_CACHE_FILE, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'market_caps': market_caps
            }, f)
    except Exception as e:
        print(f"⚠️ Erreur lors de la sauvegarde du cache de capitalisation: {e}")

def process_data(
    api_data: Dict[str, Any],
    symbols_list: List[str],
    emetteurs_map: Dict[str, str],
    mica_status_map: Dict[str, str]
) -> tuple[pd.DataFrame, List[str]]:
    """Traite les données brutes de l'API pour créer un DataFrame et des alertes."""
    records = []
    alertes = []
    
    # Charger les capitalisations précédemment mises en cache
    cached_market_caps = load_cached_market_caps()
    # Dictionnaire pour stocker les nouvelles capitalisations à mettre en cache
    new_market_caps = {}

    for symbol in symbols_list:
        coin_data = api_data['data'].get(symbol)
        price, market_cap, supply, volume = 0, 0, 0, 0

        if not coin_data:
            print(f"ℹ️  Information: Le symbole {symbol} n'a pas été trouvé dans la réponse de l'API.")
        else:
            if isinstance(coin_data, list):
                if not coin_data:
                    print(f"⚠️  Alerte: Le symbole {symbol} a retourné une liste vide de l'API.")
                else:
                    coin_data = coin_data[0]
            
            quote = coin_data.get('quote', {}).get('USD', {})
            price = quote.get('price') or 0
            
            # Accepter la capitalisation même si elle n'est pas vérifiée
            market_cap = quote.get('market_cap') or coin_data.get('self_reported_market_cap') or 0
            
            supply = coin_data.get('circulating_supply') or coin_data.get('self_reported_circulating_supply') or 0
            volume = quote.get('volume_24h') or 0
        
        # Si la capitalisation est nulle, essayer d'utiliser des sources alternatives
        if market_cap == 0:
            # 1. Vérifier les valeurs de repli définies manuellement
            if symbol in MARKET_CAP_FALLBACKS:
                market_cap = MARKET_CAP_FALLBACKS[symbol]
                print(f"ℹ️  Utilisation de la capitalisation de repli pour {symbol}: {market_cap}")
            # 2. Sinon, essayer d'utiliser la dernière valeur mise en cache
            elif symbol in cached_market_caps:
                market_cap = cached_market_caps[symbol]
                print(f"ℹ️  Utilisation de la capitalisation en cache pour {symbol}: {market_cap}")
        
        # Mettre en cache la capitalisation si elle est non nulle
        if market_cap > 0:
            new_market_caps[symbol] = market_cap

        # Note: volume_7d is calculated as 7 * volume_24h, which is an estimation
        volume_7d = volume * 7
        ratio = round((volume_7d / market_cap) * 100, 2) if market_cap > 0 else 0

        if market_cap == 0 and symbol in api_data.get('data', {}) and api_data['data'].get(symbol):
             # Check if the symbol was actually in the list requested, to avoid alerts for symbols not intended
             if symbol in symbols_list:
                 alertes.append(f"⚠ {symbol} ({emetteurs_map.get(symbol, 'N/A')}) : Pas de capitalisation renseignée.")

        records.append({
            "Nom": symbol,
            "Émetteur": emetteurs_map.get(symbol, "N/A"),
            "Conformité MiCA": mica_status_map.get(symbol, "À vérifier"),
            "Prix (USD)": round(price, 4),
            "Capitalisation (USD)": round(market_cap, 2),
            "Offre en circulation": round(supply, 2),
            "Volume 24h (USD)": round(volume, 2),
            "Volume hebdo (USD)": round(volume_7d, 2), # Keep this column name as used in charts
            "Volume hebdo / Cap (%)": ratio
        })
    
    # Sauvegarder les nouvelles capitalisations en cache
    save_cached_market_caps({**cached_market_caps, **new_market_caps})
    
    return pd.DataFrame(records), alertes


# update_history function is removed

def create_excel_report(
    df_display_final: pd.DataFrame,
    # historique_df is removed
    symbols_list: List[str], # symbols_list is needed for chart color mapping
    excel_file_path: Path
):
    """Crée le rapport Excel avec données et graphiques sur des feuilles séparées."""
    with pd.ExcelWriter(str(excel_file_path), engine='xlsxwriter') as writer:
        workbook = writer.book

        # Define the format for sheet titles
        sheet_title_format = workbook.add_format({'bold': True, 'font_size': 14, 'align': 'left', 'valign': 'vcenter'})

        # --- Feuille 1 (Graphiques Combinés) ---
        worksheet_charts_combined = workbook.add_worksheet(CHARTS_COMBINED_SHEET_NAME)
        writer.sheets[CHARTS_COMBINED_SHEET_NAME] = worksheet_charts_combined
        worksheet_charts_combined.write('A1', f"📊 Graphiques - Stablecoins Euro - {datetime.today().strftime('%d/%m/%Y')}", sheet_title_format)
        worksheet_charts_combined.set_row(0, 30)


        # --- Feuille 2 (Tableau de Données) ---
        worksheet_data_table = workbook.add_worksheet(DATA_TABLE_SHEET_NAME)
        writer.sheets[DATA_TABLE_SHEET_NAME] = worksheet_data_table
        date_str = datetime.today().strftime('%d/%m/%Y')
        worksheet_data_table.write('A1', f"🔎 Tableau de Données - Stablecoins Euro - {date_str} (MiCA applicable depuis 30/06/2024)", sheet_title_format)
        worksheet_data_table.set_row(0, 30)

        # Write DataFrame (Table) to the Data Table sheet
        df_display_final.to_excel(writer, sheet_name=DATA_TABLE_SHEET_NAME, startrow=1, index=False)

        # Set column widths for Data Table sheet
        worksheet_data_table.set_column('A:A', 12)
        worksheet_data_table.set_column('B:B', 20)
        worksheet_data_table.set_column('C:C', 22)

        try:
            # Adjust width for numeric columns starting from 'Prix (USD)' on Data Table sheet
            prix_usd_col_index = df_display_final.columns.get_loc('Prix (USD)')
            start_numeric_col_letter = chr(ord('A') + prix_usd_col_index)
            end_numeric_col_letter = chr(ord('A') + len(df_display_final.columns) - 1)
            if start_numeric_col_letter <= end_numeric_col_letter:
                worksheet_data_table.set_column(f'{start_numeric_col_letter}:{end_numeric_col_letter}', 18)
        except KeyError:
            print("Avertissement: La colonne 'Prix (USD)' n'a pas été trouvée pour l'ajustement de la largeur sur la feuille 'Tableau Données'.")

        # --- Configuration pour forcer le tableau sur une seule page PDF ---
        worksheet_data_table.fit_to_pages(1, 1)
        print(f"ℹ️  La feuille '{DATA_TABLE_SHEET_NAME}' est configurée pour tenir sur une seule page.")
        # --------------------------------------------------------------------

        # Data references for charts will point to this sheet (Tableau Données)
        # Note: startrow=1 means data headers are in row 2 (index 1), data starts in row 3 (index 2)
        header_excel_row_data_table = 1 + 1 # Excel row 2 (0-indexed row 1)
        first_data_excel_row_data_table = header_excel_row_data_table + 1 # Excel row 3 (0-indexed row 2)
        num_data_rows = len(df_display_final)
        last_data_excel_row_data_table = first_data_excel_row_data_table + num_data_rows - 1 if num_data_rows > 0 else first_data_excel_row_data_table

        # Get column letters based on header position in the *final display DataFrame*
        try:
            col_letter_nom = chr(ord('A') + df_display_final.columns.get_loc('Nom'))
            col_letter_cap = chr(ord('A') + df_display_final.columns.get_loc('Capitalisation (USD)'))
            col_letter_ratio = chr(ord('A') + df_display_final.columns.get_loc('Volume hebdo / Cap (%)'))
        except KeyError as e:
            print(f"❌ Erreur: Colonne requise pour graphique non trouvée dans le DataFrame final : {e}")
            # Provide default letters, although this might result in incorrect charts
            col_letter_nom, col_letter_cap, col_letter_ratio = 'A', 'E', 'I'


        # --- CAMEMBERT CAPITALISATION (sur la feuille Graphiques Combinés) ---
        chart_cap = workbook.add_chart({'type': 'pie'})
        # Filter data for pie chart to include only stablecoins with Market Cap > 0
        df_pie_data_source = df_display_final[df_display_final["Capitalisation (USD)"] > 0].reset_index(drop=True)
        num_rows_for_pie = len(df_pie_data_source)

        if num_rows_for_pie > 0:
             # The references point back to the DATA_TABLE_SHEET_NAME
             # Note: These references correctly point to the rows in the sorted DATA_TABLE_SHEET_NAME
             # that correspond to the filtered data (top N by Cap > 0)
             chart_cap.add_series({
                'name':       f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_cap}${header_excel_row_data_table}",
                'categories': f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_nom}${first_data_excel_row_data_table}:${col_letter_nom}${first_data_excel_row_data_table + num_rows_for_pie - 1}",
                'values':     f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_cap}${first_data_excel_row_data_table}:${col_letter_cap}${first_data_excel_row_data_table + num_rows_for_pie - 1}",
                'points': [
                    {'fill': {'color': COULEURS[i % len(COULEURS)]}} for i in range(num_rows_for_pie)
                ],
                'data_labels': {'percentage': True, 'leader_lines': True, 'value':False, 'category':True, 'position': 'outside_end'},
            })

             chart_cap.set_title({'name': 'Parts de marché par Capitalisation (pour Cap > 0)'})
             chart_cap.set_legend({'position': 'right'})
             chart_cap.set_style(10)
             # Insert chart on the CHARTS_COMBINED_SHEET_NAME
             worksheet_charts_combined.insert_chart('B2', chart_cap, {'x_scale': 1.2, 'y_scale': 1.2}) # Insert at B2

        else:
             print("ℹ️  Pas assez de données avec Capitalisation > 0 pour créer le graphique Camembert.")


        # --- DIAGRAMME Volume hebdo / Cap (sur la feuille Graphiques Combinés) ---
        chart_ratio = workbook.add_chart({'type': 'column'})
        if num_data_rows > 0:
            # References point back to the entire data range in DATA_TABLE_SHEET_NAME
            chart_ratio.add_series({
                'name':       f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_ratio}${header_excel_row_data_table}",
                'categories': f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_nom}${first_data_excel_row_data_table}:${col_letter_nom}${last_data_excel_row_data_table}",
                'values':     f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_ratio}${first_data_excel_row_data_table}:${col_letter_ratio}${last_data_excel_row_data_table}",
                'fill':       {'color': '#7D3C98'},
                'data_labels': {'value': True, 'num_format': '#,##0.0" %"', 'position': 'outside_end'}
            })
            chart_ratio.set_title({'name': 'Ratio Volume hebdomadaire / Capitalisation (%)'})
            chart_ratio.set_x_axis({'name': 'Stablecoin (Trié par Capitalisation)', 'label_position': 'low', 'major_tick_mark': 'none'})
            chart_ratio.set_y_axis({'name': 'Ratio (%)', 'major_gridlines': {'visible': False}, 'num_format': '#,##0" %"'})
            chart_ratio.set_legend({'none': True})
            chart_ratio.set_style(11)
            # Insert chart on the CHARTS_COMBINED_SHEET_NAME below the pie chart
            worksheet_charts_combined.insert_chart('B20', chart_ratio, {'x_scale': 1.2, 'y_scale': 1.2}) # Insert at B20
        else:
             print("ℹ️  Pas de données à afficher pour le graphique Volume/Cap.")


        # --- History sheet creation and content is removed ---
        # worksheet_hist = workbook.add_worksheet(HISTORY_SHEET_NAME)
        # writer.sheets[HISTORY_SHEET_NAME] = worksheet_hist
        # ... history chart logic removed ...


        # Get column letters based on header position in the *final display DataFrame*
        try:
            col_letter_nom = chr(ord('A') + df_display_final.columns.get_loc('Nom'))
            col_letter_cap = chr(ord('A') + df_display_final.columns.get_loc('Capitalisation (USD)'))
            col_letter_ratio = chr(ord('A') + df_display_final.columns.get_loc('Volume hebdo / Cap (%)'))
        except KeyError as e:
            print(f"❌ Erreur: Colonne requise pour graphique non trouvée dans le DataFrame final : {e}")
            # Provide default letters, although this might result in incorrect charts
            col_letter_nom, col_letter_cap, col_letter_ratio = 'A', 'E', 'I'


        # --- CAMEMBERT CAPITALISATION (sur la feuille Graphiques Combinés) ---
        chart_cap = workbook.add_chart({'type': 'pie'})
        # Filter data for pie chart to include only stablecoins with Market Cap > 0
        df_pie_data_source = df_display_final[df_display_final["Capitalisation (USD)"] > 0].reset_index(drop=True)
        num_rows_for_pie = len(df_pie_data_source)

        if num_rows_for_pie > 0:
             # The references point back to the DATA_TABLE_SHEET_NAME
             # Note: These references correctly point to the rows in the sorted DATA_TABLE_SHEET_NAME
             # that correspond to the filtered data (top N by Cap > 0)
             chart_cap.add_series({
                'name':       f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_cap}${header_excel_row_data_table}",
                'categories': f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_nom}${first_data_excel_row_data_table}:${col_letter_nom}${first_data_excel_row_data_table + num_rows_for_pie - 1}",
                'values':     f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_cap}${first_data_excel_row_data_table}:${col_letter_cap}${first_data_excel_row_data_table + num_rows_for_pie - 1}",
                'points': [
                    {'fill': {'color': COULEURS[i % len(COULEURS)]}} for i in range(num_rows_for_pie)
                ],
                'data_labels': {'percentage': True, 'leader_lines': True, 'value':False, 'category':True, 'position': 'outside_end'},
            })

             chart_cap.set_title({'name': 'Parts de marché par Capitalisation (pour Cap > 0)'})
             chart_cap.set_legend({'position': 'right'})
             chart_cap.set_style(10)
             # Insert chart on the CHARTS_COMBINED_SHEET_NAME
             worksheet_charts_combined.insert_chart('B2', chart_cap, {'x_scale': 1.2, 'y_scale': 1.2}) # Insert at B2

        else:
             print("ℹ️  Pas assez de données avec Capitalisation > 0 pour créer le graphique Camembert.")


        # --- DIAGRAMME Volume hebdo / Cap (sur la feuille Graphiques Combinés) ---
        chart_ratio = workbook.add_chart({'type': 'column'})
        if num_data_rows > 0:
            # References point back to the entire data range in DATA_TABLE_SHEET_NAME
            chart_ratio.add_series({
                'name':       f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_ratio}${header_excel_row_data_table}",
                'categories': f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_nom}${first_data_excel_row_data_table}:${col_letter_nom}${last_data_excel_row_data_table}",
                'values':     f"='{DATA_TABLE_SHEET_NAME}'!${col_letter_ratio}${first_data_excel_row_data_table}:${col_letter_ratio}${last_data_excel_row_data_table}",
                'fill':       {'color': '#7D3C98'},
                'data_labels': {'value': True, 'num_format': '#,##0.0" %"', 'position': 'outside_end'}
            })
            chart_ratio.set_title({'name': 'Ratio Volume hebdomadaire / Capitalisation (%)'})
            chart_ratio.set_x_axis({'name': 'Stablecoin (Trié par Capitalisation)', 'label_position': 'low', 'major_tick_mark': 'none'})
            chart_ratio.set_y_axis({'name': 'Ratio (%)', 'major_gridlines': {'visible': False}, 'num_format': '#,##0" %"'})
            chart_ratio.set_legend({'none': True})
            chart_ratio.set_style(11)
            # Insert chart on the CHARTS_COMBINED_SHEET_NAME below the pie chart
            worksheet_charts_combined.insert_chart('B20', chart_ratio, {'x_scale': 1.2, 'y_scale': 1.2}) # Insert at B20
        else:
             print("ℹ️  Pas de données à afficher pour le graphique Volume/Cap.")


        # --- History sheet creation and content is removed ---
        # worksheet_hist = workbook.add_worksheet(HISTORY_SHEET_NAME)
        # writer.sheets[HISTORY_SHEET_NAME] = worksheet_hist
        # ... history chart logic removed ...


def create_mysql_export(df_display_final: pd.DataFrame, sql_file_path: Path):
    """Crée un fichier SQL avec CREATE TABLE et INSERT statements pour MySQL."""
    try:
        with open(sql_file_path, 'w', encoding='utf-8') as f:
            # En-tête du fichier SQL
            f.write("-- =====================================================\n")
            f.write("-- Export des données Stablecoins EUR vers MySQL\n")
            f.write(f"-- Généré le : {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("-- =====================================================\n\n")

            # Suppression de la table si elle existe
            f.write("DROP TABLE IF EXISTS stablecoins_eur;\n\n")

            # Création de la table
            f.write("CREATE TABLE stablecoins_eur (\n")
            f.write("    id INT AUTO_INCREMENT PRIMARY KEY,\n")
            f.write("    nom VARCHAR(20) NOT NULL,\n")
            f.write("    emetteur VARCHAR(100) NOT NULL,\n")
            f.write("    conformite_mica VARCHAR(20) DEFAULT 'À vérifier',\n")
            f.write("    prix_usd DECIMAL(10, 6) DEFAULT 0.0,\n")
            f.write("    capitalisation_usd DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    offre_circulation DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    volume_24h_usd DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    volume_hebdo_usd DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    volume_hebdo_cap_ratio DECIMAL(8, 4) DEFAULT 0.0,\n")
            f.write("    date_maj TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n")
            f.write("    INDEX idx_nom (nom),\n")
            f.write("    INDEX idx_emetteur (emetteur),\n")
            f.write("    INDEX idx_capitalisation (capitalisation_usd),\n")
            f.write("    INDEX idx_date_maj (date_maj)\n")
            f.write(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n")

            # Insertion des données
            f.write("-- Insertion des données\n")
            f.write("INSERT INTO stablecoins_eur (\n")
            f.write("    nom, emetteur, conformite_mica, prix_usd, capitalisation_usd,\n")
            f.write("    offre_circulation, volume_24h_usd, volume_hebdo_usd, volume_hebdo_cap_ratio\n")
            f.write(") VALUES\n")

            # Génération des valeurs INSERT
            insert_values = []
            for _, row in df_display_final.iterrows():
                nom = row['Nom'].replace("'", "''")  # Échapper les apostrophes
                emetteur = row['Émetteur'].replace("'", "''")
                conformite = row['Conformité MiCA'].replace("'", "''")
                prix = row['Prix (USD)']
                cap = row['Capitalisation (USD)']
                offre = row['Offre en circulation']
                vol_24h = row['Volume 24h (USD)']
                vol_hebdo = row['Volume hebdo (USD)']
                ratio = row['Volume hebdo / Cap (%)']

                insert_values.append(
                    f"    ('{nom}', '{emetteur}', '{conformite}', {prix}, {cap}, "
                    f"{offre}, {vol_24h}, {vol_hebdo}, {ratio})"
                )

            f.write(',\n'.join(insert_values))
            f.write(";\n\n")

            # Requêtes d'exemple utiles
            f.write("-- =====================================================\n")
            f.write("-- Requêtes d'exemple utiles\n")
            f.write("-- =====================================================\n\n")

            f.write("-- Top 10 par capitalisation\n")
            f.write("SELECT nom, emetteur, capitalisation_usd, conformite_mica\n")
            f.write("FROM stablecoins_eur\n")
            f.write("ORDER BY capitalisation_usd DESC\n")
            f.write("LIMIT 10;\n\n")

            f.write("-- Stablecoins conformes MiCA\n")
            f.write("SELECT nom, emetteur, capitalisation_usd\n")
            f.write("FROM stablecoins_eur\n")
            f.write("WHERE conformite_mica = 'X'\n")
            f.write("ORDER BY capitalisation_usd DESC;\n\n")

            f.write("-- Statistiques globales\n")
            f.write("SELECT \n")
            f.write("    COUNT(*) as nombre_stablecoins,\n")
            f.write("    SUM(capitalisation_usd) as capitalisation_totale,\n")
            f.write("    AVG(capitalisation_usd) as capitalisation_moyenne,\n")
            f.write("    SUM(volume_24h_usd) as volume_24h_total,\n")
            f.write("    AVG(volume_hebdo_cap_ratio) as ratio_moyen\n")
            f.write("FROM stablecoins_eur;\n\n")

            f.write("-- Répartition par émetteur\n")
            f.write("SELECT \n")
            f.write("    emetteur,\n")
            f.write("    COUNT(*) as nombre_tokens,\n")
            f.write("    SUM(capitalisation_usd) as capitalisation_totale,\n")
            f.write("    AVG(prix_usd) as prix_moyen\n")
            f.write("FROM stablecoins_eur\n")
            f.write("GROUP BY emetteur\n")
            f.write("ORDER BY capitalisation_totale DESC;\n\n")

            f.write("-- Tokens avec le plus fort ratio volume/capitalisation\n")
            f.write("SELECT nom, emetteur, volume_hebdo_cap_ratio, capitalisation_usd\n")
            f.write("FROM stablecoins_eur\n")
            f.write("WHERE capitalisation_usd > 0\n")
            f.write("ORDER BY volume_hebdo_cap_ratio DESC\n")
            f.write("LIMIT 10;\n\n")

        print(f"✅ Export MySQL généré : '{sql_file_path}'")
        return True

    except Exception as e:
        print(f"❌ Erreur lors de la création du fichier SQL : {e}")
        import traceback
        traceback.print_exc()
        return False


def create_mysql_history_table(sql_file_path: Path):
    """Ajoute au fichier SQL une table d'historique pour suivre l'évolution des données."""
    try:
        with open(sql_file_path, 'a', encoding='utf-8') as f:
            f.write("-- =====================================================\n")
            f.write("-- Table d'historique pour le suivi temporel\n")
            f.write("-- =====================================================\n\n")

            f.write("DROP TABLE IF EXISTS stablecoins_eur_historique;\n\n")

            f.write("CREATE TABLE stablecoins_eur_historique (\n")
            f.write("    id INT AUTO_INCREMENT PRIMARY KEY,\n")
            f.write("    nom VARCHAR(20) NOT NULL,\n")
            f.write("    emetteur VARCHAR(100) NOT NULL,\n")
            f.write("    conformite_mica VARCHAR(20) DEFAULT 'À vérifier',\n")
            f.write("    prix_usd DECIMAL(10, 6) DEFAULT 0.0,\n")
            f.write("    capitalisation_usd DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    offre_circulation DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    volume_24h_usd DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    volume_hebdo_usd DECIMAL(20, 2) DEFAULT 0.0,\n")
            f.write("    volume_hebdo_cap_ratio DECIMAL(8, 4) DEFAULT 0.0,\n")
            f.write("    date_snapshot DATE NOT NULL,\n")
            f.write("    heure_snapshot TIME NOT NULL,\n")
            f.write("    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n")
            f.write("    INDEX idx_nom_date (nom, date_snapshot),\n")
            f.write("    INDEX idx_date_snapshot (date_snapshot),\n")
            f.write("    INDEX idx_capitalisation_hist (capitalisation_usd),\n")
            f.write("    UNIQUE KEY unique_nom_date (nom, date_snapshot)\n")
            f.write(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n\n")

            f.write("-- Procédure pour insérer les données actuelles dans l'historique\n")
            f.write("DELIMITER //\n")
            f.write("CREATE PROCEDURE InsererHistorique()\n")
            f.write("BEGIN\n")
            f.write("    INSERT INTO stablecoins_eur_historique (\n")
            f.write("        nom, emetteur, conformite_mica, prix_usd, capitalisation_usd,\n")
            f.write("        offre_circulation, volume_24h_usd, volume_hebdo_usd, volume_hebdo_cap_ratio,\n")
            f.write("        date_snapshot, heure_snapshot\n")
            f.write("    )\n")
            f.write("    SELECT \n")
            f.write("        nom, emetteur, conformite_mica, prix_usd, capitalisation_usd,\n")
            f.write("        offre_circulation, volume_24h_usd, volume_hebdo_usd, volume_hebdo_cap_ratio,\n")
            f.write("        CURDATE(), CURTIME()\n")
            f.write("    FROM stablecoins_eur\n")
            f.write("    ON DUPLICATE KEY UPDATE\n")
            f.write("        prix_usd = VALUES(prix_usd),\n")
            f.write("        capitalisation_usd = VALUES(capitalisation_usd),\n")
            f.write("        offre_circulation = VALUES(offre_circulation),\n")
            f.write("        volume_24h_usd = VALUES(volume_24h_usd),\n")
            f.write("        volume_hebdo_usd = VALUES(volume_hebdo_usd),\n")
            f.write("        volume_hebdo_cap_ratio = VALUES(volume_hebdo_cap_ratio),\n")
            f.write("        heure_snapshot = VALUES(heure_snapshot);\n")
            f.write("END //\n")
            f.write("DELIMITER ;\n\n")

            f.write("-- Requêtes d'analyse historique\n")
            f.write("-- Évolution de la capitalisation sur les 30 derniers jours\n")
            f.write("/*\n")
            f.write("SELECT \n")
            f.write("    nom,\n")
            f.write("    date_snapshot,\n")
            f.write("    capitalisation_usd,\n")
            f.write("    LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot) as cap_precedente,\n")
            f.write("    ROUND(((capitalisation_usd - LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot)) / LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot)) * 100, 2) as variation_pct\n")
            f.write("FROM stablecoins_eur_historique\n")
            f.write("WHERE date_snapshot >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)\n")
            f.write("ORDER BY nom, date_snapshot;\n")
            f.write("*/\n\n")

        print(f"✅ Table d'historique MySQL ajoutée au fichier : '{sql_file_path}'")
        return True

    except Exception as e:
        print(f"❌ Erreur lors de l'ajout de la table d'historique : {e}")
        return False


# --- SCRIPT PRINCIPAL ---
def main():
    """Fonction principale du script."""
    print("🚀 Démarrage du script d'observation des stablecoins EUR...")
    print(f"Date actuelle: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"ℹ️  Rappel: Les règles MiCA pour les stablecoins (ARTs & EMTs) sont applicables depuis le 30 juin 2024.")
    print(f"ℹ️  Veuillez vérifier et mettre à jour la section 'MICA_STATUS' dans le script avec les informations les plus récentes.\n")

    api_data = fetch_coin_data(API_KEY, SYMBOLS)
    if not api_data:
        print("❌ Arrêt du script en raison d'une erreur API.")
        return

    df_current, alertes = process_data(api_data, SYMBOLS, EMETTEURS, MICA_STATUS)

    cols_display_order = ["Nom", "Émetteur", "Conformité MiCA"] + \
                         [col for col in df_current.columns if col not in ["Nom", "Émetteur", "Conformité MiCA"]]

    # Tri principal du tableau par "Capitalisation (USD)" décroissant
    df_display_final = df_current[cols_display_order].sort_values(by="Capitalisation (USD)", ascending=False).reset_index(drop=True)

    # Updated message to reflect the new sheet name for the table
    print("ℹ️  Le tableau principal dans la feuille 'Tableau Données' est désormais trié par 'Capitalisation (USD)' (décroissant).")

    # History update section is removed
    # history_file_path = Path(HISTORY_FILENAME)
    # historique_df = update_history(df_current, SYMBOLS, history_file_path)
    # print(f"📈 Historique mis à jour dans '{history_file_path}'.")
    historique_df = pd.DataFrame() # Create an empty df just to pass something if needed, though it's not used now


    excel_file_path = Path(EXCEL_FILENAME)
    pdf_file_path = Path(PDF_FILENAME)
    sql_file_path = Path(SQL_FILENAME)
    pdf_conversion_success = False # Flag to track PDF conversion status

    # Génération du fichier SQL MySQL
    print("🗄️  Génération de l'export MySQL...")
    mysql_success = create_mysql_export(df_display_final, sql_file_path)

    # Ajout de la table d'historique
    if mysql_success:
        create_mysql_history_table(sql_file_path)

    try:
        # Call create_excel_report without historique_df
        create_excel_report(df_display_final, symbols_list=SYMBOLS, excel_file_path=excel_file_path)
        print(f"✅ Rapport Excel généré : '{excel_file_path}'")
    except Exception as e:
        print(f"❌ Erreur lors de la création du fichier Excel : {e}")
        import traceback
        traceback.print_exc()
        # Ne pas arrêter le script si l'Excel échoue mais que le SQL a réussi
        if not mysql_success:
            return

    if alertes:
        print("\n⚠️ Alertes détectées :")
        for alerte in alertes:
            print(alerte)


    # --- Section pour la conversion PDF ---
    print(f"🔄 Tentative de conversion de '{excel_file_path.name}' en PDF...")
    print(f"ℹ️  La conversion PDF nécessite l'installation de LibreOffice et son accessibilité dans le PATH.")


    soffice_command = 'soffice' # Default: hope it's in PATH
    # Common macOS path for LibreOffice soffice executable after .dmg install
    mac_soffice_path = '/Applications/LibreOffice.app/Contents/MacOS/soffice'

    try:
        # Check if the common macOS path exists and is executable
        if platform.system() == "Darwin" and Path(mac_soffice_path).exists():
             soffice_command = mac_soffice_path
             print(f"ℹ️  LibreOffice trouvé à l'emplacement standard macOS : {soffice_command}")
        else:
             pass # Keep silent unless FileNotFoundError occurs


        # Determine the output directory for the PDF (same as Excel)
        output_dir = excel_file_path.parent

        # Use subprocess to call LibreOffice for conversion
        # Added '--nologo' and '--nodefault-instancemanager' to potentially speed up/clean output
        command = [soffice_command, '--headless', '--convert-to', 'pdf', '--outdir', str(output_dir.resolve()), str(excel_file_path.resolve())]

        # Execute the command
        # Increased timeout slightly as conversion can take a moment
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=90)

        # Check if the PDF file was created
        if pdf_file_path.exists():
             print(f"✅ Conversion PDF réussie : '{pdf_file_path.name}'")
             pdf_conversion_success = True
        else:
             print(f"❌ Conversion PDF signalée comme réussie par soffice, mais le fichier '{pdf_file_path.name}' n'a pas été trouvé.")
             print(f"   Sortie Soffice (stdout):\n{result.stdout}")
             print(f"   Erreur Soffice (stderr):\n{result.stderr}")


    except FileNotFoundError:
        print(f"❌ Erreur : L'exécutable de LibreOffice '{soffice_command}' n'a pas été trouvé.")
        if platform.system() == "Darwin":
            print(f"   Si LibreOffice est installé via le .dmg, assurez-vous qu'il est bien dans le dossier /Applications.")
            print(f"   L'emplacement attendu pour 'soffice' est souvent : {mac_soffice_path}")
            print(f"   Sinon, si installé via Homebrew, assurez-vous que le répertoire de bin de Homebrew est dans votre PATH.")
        else:
             print("   Veuillez installer LibreOffice et vous assurer que 'soffice' est accessible dans le PATH de votre système.")
             print("   Instructions pour ajouter au PATH (variable d'environnement) dépendent de votre OS.")

    except subprocess.CalledProcessError as e:
        print(f"❌ Erreur lors de l'exécution de LibreOffice pour la conversion PDF (Code de retour {e.returncode}).")
        print(f"   Vérifiez le message d'erreur de Soffice ci-dessus.")
        print(f"   Sortie Soffice (stdout):\n{e.stdout}")
        print(f"   Erreur Soffice (stderr):\n{e.stderr}")
    except subprocess.TimeoutExpired:
        print("❌ Erreur : Le processus de conversion PDF avec Soffice a dépassé le délai imparti (90 secondes).")
        print("   Le fichier Excel est peut-être trop complexe ou Soffice rencontre un problème.")
    except Exception as e:
        print(f"❌ Une erreur inattendue est survenue lors de la conversion PDF : {e}")


    # --- Section pour l'ouverture automatique ---
    # Tentative d'ouverture du PDF si il existe et conversion réussie, sinon tentative d'ouverture de l'Excel
    file_to_open = pdf_file_path if pdf_conversion_success and pdf_file_path.exists() else excel_file_path

    if file_to_open.exists():
        print(f"📂 Tentative d'ouverture de '{file_to_open.name}'.")
        try:
            if platform.system() == "Darwin": # macOS
                os.system(f'open "{file_to_open.resolve()}"')
            elif platform.system() == "Windows":
                os.startfile(str(file_to_open.resolve()))
            elif platform.system() == "Linux":
                os.system(f'xdg-open "{file_to_open.resolve()}"')
            else:
                 print(f"ℹ️  Plateforme non supportée pour l'ouverture automatique. Ouvrez manuellement : {file_to_open.resolve()}")

        except Exception as e:
             print(f"ℹ️  Erreur lors de l'ouverture automatique du fichier : {e}")
             print(f"   Vous pouvez l'ouvrir manuellement ici : {file_to_open.resolve()}")
    else:
        print(f"ℹ️  Le fichier PDF ('{pdf_file_path.name}') (conversion non réussie ou fichier introuvable) ni le fichier Excel ('{excel_file_path.name}') n'ont été trouvés pour l'ouverture automatique.")

    # Affichage des informations sur le fichier SQL généré
    if mysql_success and sql_file_path.exists():
        print(f"\n📊 EXPORT MYSQL GÉNÉRÉ")
        print(f"📁 Fichier : {sql_file_path.resolve()}")
        print(f"📏 Taille : {sql_file_path.stat().st_size / 1024:.1f} KB")
        print(f"🔢 Nombre de stablecoins : {len(df_display_final)}")
        print(f"💰 Capitalisation totale : ${df_display_final['Capitalisation (USD)'].sum():,.2f}")
        print(f"\n🗄️  INSTRUCTIONS D'UTILISATION :")
        print(f"   1. Connectez-vous à votre serveur MySQL")
        print(f"   2. Créez une base de données : CREATE DATABASE stablecoins_db;")
        print(f"   3. Utilisez la base : USE stablecoins_db;")
        print(f"   4. Exécutez le fichier SQL : SOURCE {sql_file_path.resolve()};")
        print(f"   5. Ou importez via phpMyAdmin/MySQL Workbench")
        print(f"\n📈 Le fichier contient :")
        print(f"   • Table principale 'stablecoins_eur' avec les données actuelles")
        print(f"   • Table d'historique 'stablecoins_eur_historique' pour le suivi temporel")
        print(f"   • Procédure stockée 'InsererHistorique()' pour automatiser l'historique")
        print(f"   • Requêtes d'exemple pour l'analyse des données")

    print(f"\n🎯 RÉSUMÉ DE L'EXÉCUTION :")
    print(f"   ✅ Données récupérées depuis CoinMarketCap")
    print(f"   {'✅' if mysql_success else '❌'} Export MySQL : {sql_file_path.name}")
    print(f"   {'✅' if excel_file_path.exists() else '❌'} Rapport Excel : {excel_file_path.name}")
    print(f"   {'✅' if pdf_conversion_success else '❌'} Conversion PDF : {pdf_file_path.name}")
    print(f"\n🚀 Script terminé avec succès !")


if __name__ == "__main__":
    main()

