import requests
import pandas as pd
import os
import platform
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional
import subprocess # Pour appeler LibreOffice
import shutil     # Pour trouver l'exécutable de LibreOffice

# --- CONFIGURATION --- (Identique à la version précédente)
API_KEY = os.getenv("CMC_API_KEY", "0e53eb7b-4d61-4604-9923-9dc343a96b74")

if API_KEY == "0e53eb7b-4d61-4604-9923-9dc343a96b74":
    print("⚠️  Attention : Vous utilisez une clé API par défaut...")

SYMBOLS: List[str] = [
    "EURC", "EURS", "EURT", "agEUR", "PAR", "EURL", "EURE", "AEUR",
    "CEUR", "EURR", "EURQ", "VEUR", "DEURO", "SEUR", "EUROE", "<PERSON>E<PERSON>",
    "EUROI"
]

EMETTEURS: Dict[str, str] = {
    "EURC": "Circle", "EURS": "Stasis", "EURT": "Tether", "agEUR": "Angle Protocol",
    "PAR": "Mimo Capital", "EURL": "Lugh / Casino Group", "EURE": "Monerium",
    "AEUR": "Anchored Coins", "CEUR": "Celo Foundation", "EURR": "StablR",
    "EURQ": "Quantoz", "VEUR": "VNX", "DEURO": "Decentralized Euros",
    "SEUR": "Synthetix / Jarvis", "EUROE": "Membrane Finance", "EEUR": "e-Money",
    "EUROI": "Banking Circle"
}

MICA_STATUS: Dict[str, str] = {
    "EURC": "Autorisé (Irlande)", "EURS": "Intention déclarée", "EURT": "À vérifier",
    "agEUR": "À vérifier", "PAR": "Intention déclarée (via Mimo Capital)",
    "EURL": "Autorisé (France)", "EURE": "Autorisé (Islande, EEE)", "AEUR": "Intention déclarée",
    "CEUR": "À vérifier", "EURR": "À vérifier", "EURQ": "Intention déclarée",
    "VEUR": "À vérifier", "DEURO": "À vérifier", "SEUR": "À vérifier",
    "EUROE": "Intention déclarée", "EEUR": "À vérifier", "EUROI": "À vérifier"
}

COULEURS: List[str] = [
    '#4E79A7', '#F28E2B', '#E15759', '#76B7B2', '#59A14F', '#EDC948',
    '#B07AA1', '#FF9DA7', '#9C755F', '#BAB0AC', '#A0CBE8', '#FFBE7D',
    '#F4B3B3', '#BDE0DA', '#A2D99E', '#FCE29A', '#D3B6CD', '#FFCFD2',
    '#CBBBAF', '#DCD6D4'
]

EXCEL_FILENAME = "Rapport_Stablecoins_EUR.xlsx"
HISTORY_FILENAME = "historique_market_cap.csv"
DATA_SHEET_NAME = "Données"
HISTORY_SHEET_NAME = "Historique"
PDF_FILENAME = "Rapport_Stablecoins_EUR.pdf" # Nom du fichier PDF de sortie

# --- FONCTIONS UTILITAIRES ---
# fetch_coin_data, process_data, update_history (Identiques à la version précédente)
def fetch_coin_data(api_key: str, symbols_list: List[str]) -> Optional[Dict[str, Any]]:
    # ... (code inchangé) ...
    headers = {
        'Accepts': 'application/json',
        'X-CMC_PRO_API_KEY': api_key,
    }
    params = {
        'symbol': ','.join(symbols_list),
        'convert': 'USD'
    }
    url = "https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest"

    try:
        response = requests.get(url, headers=headers, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        if 'data' not in data or not data['data']:
            error_message = data.get('status', {}).get('error_message', 'Réponse API invalide ou vide.')
            print(f"❌ Erreur API : {error_message}")
            if data.get('status', {}).get('error_code') == 1001:
                 print("❌ Vérifiez votre clé API CoinMarketCap (X-CMC_PRO_API_KEY).")
            return None
        return data
    except requests.exceptions.HTTPError as http_err:
        print(f"❌ Erreur HTTP : {http_err} - Contenu : {response.text}")
    except requests.exceptions.ConnectionError as conn_err:
        print(f"❌ Erreur de Connexion : {conn_err}")
    except requests.exceptions.Timeout as timeout_err:
        print(f"❌ Erreur de Timeout : {timeout_err}")
    except requests.exceptions.RequestException as req_err:
        print(f"❌ Erreur Requête API : {req_err}")
    except ValueError as json_err: # requests.json() peut lever ValueError
        print(f"❌ Erreur de décodage JSON : {json_err} - Réponse reçue: {response.text}")
    return None

def process_data(
    api_data: Dict[str, Any],
    symbols_list: List[str],
    emetteurs_map: Dict[str, str],
    mica_status_map: Dict[str, str]
) -> tuple[pd.DataFrame, List[str]]:
    # ... (code inchangé) ...
    records = []
    alertes = []

    for symbol in symbols_list:
        coin_data = api_data['data'].get(symbol)
        price, market_cap, supply, volume = 0, 0, 0, 0

        if not coin_data:
            # print(f"ℹ️ Information: Le symbole {symbol} n'a pas été trouvé dans la réponse de l'API.") # Optionnel
            pass
        else:
            if isinstance(coin_data, list):
                if not coin_data:
                    # print(f"⚠️ Alerte: Le symbole {symbol} a retourné une liste vide de l'API.") # Optionnel
                    pass
                else:
                    coin_data = coin_data[0]
            
            quote = coin_data.get('quote', {}).get('USD', {})
            price = quote.get('price') or 0
            market_cap = quote.get('market_cap') or 0
            supply = coin_data.get('circulating_supply') or 0
            volume = quote.get('volume_24h') or 0

        volume_7d = volume * 7
        ratio = round((volume_7d / market_cap) * 100, 2) if market_cap > 0 else 0

        if market_cap == 0 and symbol in api_data['data'] and coin_data :
            alertes.append(f"⚠ {symbol} ({emetteurs_map.get(symbol, 'N/A')}) : Pas de capitalisation renseignée.")

        records.append({
            "Nom": symbol,
            "Émetteur": emetteurs_map.get(symbol, "N/A"),
            "Conformité MiCA": mica_status_map.get(symbol, "À vérifier"),
            "Prix (USD)": round(price, 4),
            "Capitalisation (USD)": round(market_cap, 2),
            "Offre en circulation": round(supply, 2),
            "Volume 24h (USD)": round(volume, 2),
            "Volume hebdo (USD)": round(volume_7d, 2),
            "Volume hebdo / Cap (%)": ratio
        })
    return pd.DataFrame(records), alertes

def update_history(df_current: pd.DataFrame, symbols_list: List[str], history_file_path: Path) -> pd.DataFrame:
    # ... (code inchangé) ...
    today_iso = datetime.today().strftime('%Y-%m-%d')

    if history_file_path.exists():
        historique_df = pd.read_csv(history_file_path)
    else:
        historique_df = pd.DataFrame(columns=["Date"] + symbols_list)

    for symbol in symbols_list:
        if symbol not in historique_df.columns:
            historique_df[symbol] = 0 

    nouvelle_ligne_data = {"Date": today_iso}
    df_current_indexed = df_current.set_index("Nom")
    for symbol in symbols_list:
        if symbol in df_current_indexed.index:
            cap_value = df_current_indexed.loc[symbol, "Capitalisation (USD)"]
            nouvelle_ligne_data[symbol] = cap_value if pd.notnull(cap_value) else 0
        else:
            nouvelle_ligne_data[symbol] = 0


    nouvelle_ligne_df = pd.DataFrame([nouvelle_ligne_data])
    historique_df = pd.concat([historique_df, nouvelle_ligne_df], ignore_index=True)
    
    try:
        historique_df['Date'] = pd.to_datetime(historique_df['Date'], errors='coerce')
        historique_df = historique_df.dropna(subset=['Date'])
    except Exception as e:
        print(f"Avertissement lors de la conversion des dates dans l'historique : {e}")

    historique_df = historique_df.drop_duplicates(subset=["Date"], keep='last')
    historique_df = historique_df.sort_values(by="Date")
    
    if 'Date' in historique_df.columns and pd.api.types.is_datetime64_any_dtype(historique_df['Date']):
      historique_df['Date'] = historique_df['Date'].dt.strftime('%Y-%m-%d')
    
    historique_df.to_csv(history_file_path, index=False)
    return historique_df

def create_excel_report(
    df_display_final: pd.DataFrame,
    historique_df: pd.DataFrame,
    symbols_list: List[str],
    excel_file_path: Path
):
    """Crée le rapport Excel avec données et graphiques, et configure pour l'impression PDF."""
    with pd.ExcelWriter(str(excel_file_path), engine='xlsxwriter') as writer:
        workbook = writer.book

        # --- Feuille 'Données' ---
        worksheet_data = workbook.add_worksheet(DATA_SHEET_NAME)
        writer.sheets[DATA_SHEET_NAME] = worksheet_data

        date_str = datetime.today().strftime('%d/%m/%Y')
        title_format = workbook.add_format({'bold': True, 'font_size': 14, 'align': 'left', 'valign': 'vcenter'})
        worksheet_data.write('A1', f"🔎 Rapport hebdomadaire - Observation Stablecoins Euro - {date_str} (MiCA applicable depuis 30/06/2024)", title_format)
        worksheet_data.set_row(0, 30) # row_num est 0-indexed

        df_display_final.to_excel(writer, sheet_name=DATA_SHEET_NAME, startrow=1, index=False)
        
        worksheet_data.set_column('A:A', 12)
        worksheet_data.set_column('B:B', 20)
        worksheet_data.set_column('C:C', 22)
        
        try:
            prix_usd_col_index = df_display_final.columns.get_loc('Prix (USD)')
            start_numeric_col_letter = chr(ord('A') + prix_usd_col_index)
            end_numeric_col_letter = chr(ord('A') + len(df_display_final.columns) - 1)
            if start_numeric_col_letter <= end_numeric_col_letter:
                worksheet_data.set_column(f'{start_numeric_col_letter}:{end_numeric_col_letter}', 18)
        except KeyError:
            print("Avertissement: La colonne 'Prix (USD)' n'a pas été trouvée pour l'ajustement de la largeur.")

        header_excel_row_num = 1 + 1 # Ligne Excel (1-based) de l'en-tête du DataFrame
        first_data_excel_row_num = header_excel_row_num + 1
        num_data_rows = len(df_display_final)
        last_data_excel_row_num = first_data_excel_row_num + num_data_rows - 1 if num_data_rows > 0 else first_data_excel_row_num
        
        current_chart_insertion_excel_row_num = last_data_excel_row_num + 3
        
        col_letter_nom = chr(ord('A') + df_display_final.columns.get_loc('Nom'))
        col_letter_cap = chr(ord('A') + df_display_final.columns.get_loc('Capitalisation (USD)'))
        col_letter_ratio = chr(ord('A') + df_display_final.columns.get_loc('Volume hebdo / Cap (%)'))
        
        chart_height_excel_rows = 15 # Hauteur standard d'un graphique en lignes Excel
        chart_padding_rows = 3

        # CAMEMBERT
        chart_cap = workbook.add_chart({'type': 'pie'})
        # ... (configuration du chart_cap identique)
        df_pie_data_source = df_display_final[df_display_final["Capitalisation (USD)"] > 0]
        num_rows_for_pie = len(df_pie_data_source)

        if num_rows_for_pie > 0:
            chart_cap.add_series({
                'name':       f"='{DATA_SHEET_NAME}'!${col_letter_cap}${header_excel_row_num}",
                'categories': f"='{DATA_SHEET_NAME}'!${col_letter_nom}${first_data_excel_row_num}:${col_letter_nom}${first_data_excel_row_num + num_rows_for_pie - 1}",
                'values':     f"='{DATA_SHEET_NAME}'!${col_letter_cap}${first_data_excel_row_num}:${col_letter_cap}${first_data_excel_row_num + num_rows_for_pie - 1}",
                'points': [{'fill': {'color': COULEURS[i % len(COULEURS)]}} for i in range(num_rows_for_pie)],
                'data_labels': {'percentage': True, 'leader_lines': True, 'value':False, 'category':True, 'position': 'outside_end'},
            })
            chart_cap.set_title({'name': 'Parts de marché par Capitalisation (pour Cap > 0)'})
            chart_cap.set_legend({'position': 'right'})
            chart_cap.set_style(10)
            worksheet_data.insert_chart(f'B{current_chart_insertion_excel_row_num}', chart_cap, {'x_scale': 1.2, 'y_scale': 1.2})
            current_chart_insertion_excel_row_num += chart_height_excel_rows + chart_padding_rows

        # DIAGRAMME RATIO
        chart_ratio = workbook.add_chart({'type': 'column'})
        # ... (configuration du chart_ratio identique)
        if num_data_rows > 0:
            chart_ratio.add_series({
                'name':       f"='{DATA_SHEET_NAME}'!${col_letter_ratio}${header_excel_row_num}",
                'categories': f"='{DATA_SHEET_NAME}'!${col_letter_nom}${first_data_excel_row_num}:${col_letter_nom}${last_data_excel_row_num}",
                'values':     f"='{DATA_SHEET_NAME}'!${col_letter_ratio}${first_data_excel_row_num}:${col_letter_ratio}${last_data_excel_row_num}",
                'fill':       {'color': '#7D3C98'},
                'data_labels': {'value': True, 'num_format': '#,##0.0" %"', 'position': 'outside_end'}
            })
        chart_ratio.set_title({'name': 'Ratio Volume hebdomadaire / Capitalisation (%)'})
        chart_ratio.set_x_axis({'name': 'Stablecoin', 'label_position': 'low', 'major_tick_mark': 'none'})
        chart_ratio.set_y_axis({'name': 'Ratio (%)', 'major_gridlines': {'visible': False}, 'num_format': '#,##0" %"'})
        chart_ratio.set_legend({'none': True})
        chart_ratio.set_style(11)
        worksheet_data.insert_chart(f'B{current_chart_insertion_excel_row_num}', chart_ratio, {'x_scale': 1.2, 'y_scale': 1.2})
        last_content_row_data_sheet_idx = current_chart_insertion_excel_row_num + chart_height_excel_rows -2 # 0-indexed for print_area

        # Configuration pour l'impression PDF de la feuille "Données"
        worksheet_data.set_landscape()
        worksheet_data.fit_to_pages(1, 0) # Ajuster à 1 page en largeur, auto en hauteur
        # Définir la zone d'impression (0-indexed rows and cols)
        # Colonne K (index 10) devrait être suffisant pour la largeur des graphiques
        worksheet_data.print_area(0, 0, last_content_row_data_sheet_idx, 10) 


        # --- Feuille 'Historique' ---
        worksheet_hist = workbook.add_worksheet(HISTORY_SHEET_NAME)
        writer.sheets[HISTORY_SHEET_NAME] = worksheet_hist
        historique_df.to_excel(writer, index=False, sheet_name=HISTORY_SHEET_NAME) # Commence à A1 par défaut
        worksheet_hist.set_column('A:A', 12)
        for i, _ in enumerate(symbols_list):
             worksheet_hist.set_column(f"{chr(ord('B') + i)}:{chr(ord('B') + i)}", 15)

        last_content_row_hist_sheet_idx = 0 # Initialisation
        if len(historique_df) > 1:
            chart_hist = workbook.add_chart({'type': 'line'})
            # ... (configuration du chart_hist identique)
            num_hist_entries = len(historique_df)
            hist_header_excel_row_num = 1 
            hist_first_data_excel_row_num = hist_header_excel_row_num + 1
            hist_last_data_excel_row_num = hist_first_data_excel_row_num + num_hist_entries - 1 if num_hist_entries >0 else hist_first_data_excel_row_num

            for idx, symbol_name in enumerate(symbols_list):
                col_letter = chr(ord('B') + idx)
                if symbol_name in historique_df.columns and num_hist_entries > 0 :
                    chart_hist.add_series({
                        'name':       f"='{HISTORY_SHEET_NAME}'!${col_letter}${hist_header_excel_row_num}",
                        'categories': f"='{HISTORY_SHEET_NAME}'!$A${hist_first_data_excel_row_num}:$A${hist_last_data_excel_row_num}",
                        'values':     f"='{HISTORY_SHEET_NAME}'!${col_letter}${hist_first_data_excel_row_num}:${col_letter}${hist_last_data_excel_row_num}",
                        'line':       {'color': COULEURS[idx % len(COULEURS)]},
                    })

            chart_hist.set_title({'name': 'Évolution historique des Capitalisations'})
            chart_hist.set_x_axis({'name': 'Date', 'date_axis': True, 'label_position': 'low', 'major_tick_mark': 'none'})
            chart_hist.set_y_axis({'name': 'Capitalisation (USD)', 'major_gridlines': {'visible': True}, 'num_format': '#,##0'})
            chart_hist.set_legend({'position': 'right'})
            chart_hist.set_style(10)
            
            chart_hist_insertion_col_idx = len(symbols_list) + 2 # Index 0-based de la colonne de départ du graph historique
            chart_hist_start_col_letter = chr(ord('A') + chart_hist_insertion_col_idx)
            # Le graphique est inséré à la ligne 2 (index 1), hauteur 15 lignes.
            worksheet_hist.insert_chart(f'{chart_hist_start_col_letter}2', chart_hist, {'x_scale': 1.5, 'y_scale': 1.5})
            # La dernière ligne de contenu est soit la fin du tableau, soit la fin du graphique.
            last_content_row_hist_sheet_idx = max(num_hist_entries, 1 + chart_height_excel_rows -1 ) # num_hist_entries + header_row ; or chart_row_start_idx + chart_height -1
        else:
            last_content_row_hist_sheet_idx = len(historique_df) # Juste le tableau

        # Configuration pour l'impression PDF de la feuille "Historique"
        worksheet_hist.set_landscape()
        worksheet_hist.fit_to_pages(1, 0)
        # Dernière colonne: après la date (0) + nb symboles + marge (1) + largeur approx graph (10-15 avec scale 1.5)
        last_print_col_hist_idx = (len(symbols_list)) + 1 + int(7 * 1.5) # 7 est la largeur de base d'un graph
        worksheet_hist.print_area(0, 0, last_content_row_hist_sheet_idx, last_print_col_hist_idx)


def find_libreoffice_path() -> Optional[str]:
    """Tente de trouver l'exécutable de LibreOffice/Soffice."""
    executables = ["libreoffice", "soffice"]
    if platform.system() == "Windows":
        # Sur Windows, il est souvent dans Program Files et non dans le PATH par défaut
        program_files = os.environ.get("ProgramFiles", "C:\\Program Files")
        program_files_x86 = os.environ.get("ProgramFiles(x86)", "C:\\Program Files (x86)")
        paths_to_check = [
            Path(program_files) / "LibreOffice" / "program" / "soffice.exe",
            Path(program_files_x86) / "LibreOffice" / "program" / "soffice.exe",
        ]
        for p in paths_to_check:
            if p.exists():
                return str(p)
    # Pour Linux/MacOS ou si non trouvé dans les chemins Windows ci-dessus
    for exe in executables:
        path = shutil.which(exe)
        if path:
            return path
    return None

def export_excel_to_pdf_libreoffice(excel_filepath: Path, output_dir: Path) -> bool:
    """Exporte un fichier Excel en PDF en utilisant LibreOffice en mode headless."""
    libreoffice_exe = find_libreoffice_path()
    if not libreoffice_exe:
        print("❌ LibreOffice n'a pas été trouvé. Impossible de convertir en PDF.")
        print("   Veuillez installer LibreOffice et vous assurer qu'il est dans le PATH,")
        print("   ou que le script peut le trouver dans les emplacements courants (Windows).")
        return False

    pdf_filepath = output_dir / excel_filepath.with_suffix(".pdf").name
    print(f"⏳ Conversion en PDF avec LibreOffice : {excel_filepath.name} -> {pdf_filepath.name}...")
    
    try:
        # Commande pour convertir en PDF
        # --headless : mode sans interface utilisateur
        # --convert-to pdf : spécifie le format de sortie
        # --outdir : spécifie le répertoire de sortie
        # Il est crucial que outdir existe.
        output_dir.mkdir(parents=True, exist_ok=True)

        # Sur Windows, soffice.exe peut nécessiter que le chemin soit entre guillemets s'il contient des espaces
        # subprocess.run gère cela correctement si on passe une liste d'arguments.
        cmd = [
            libreoffice_exe,
            "--headless",
            "--convert-to", "pdf",
            "--outdir", str(output_dir.resolve()), # S'assurer que le chemin est absolu
            str(excel_filepath.resolve()) # S'assurer que le chemin est absolu
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120) # Timeout de 2 minutes

        if result.returncode == 0:
            print(f"✅ PDF généré avec succès : '{pdf_filepath}'")
            return True
        else:
            print(f"❌ Erreur lors de la conversion en PDF avec LibreOffice.")
            print(f"   Code de retour : {result.returncode}")
            print(f"   Stderr : {result.stderr}")
            print(f"   Stdout : {result.stdout}") # Peut contenir des infos utiles
            return False
    except subprocess.TimeoutExpired:
        print("❌ Timeout lors de la conversion en PDF. Le fichier est peut-être trop volumineux ou LibreOffice a un problème.")
        return False
    except FileNotFoundError: # Si l'exécutable n'est toujours pas trouvé malgré shutil.which
        print(f"❌ Erreur : L'exécutable LibreOffice '{libreoffice_exe}' n'a pas pu être lancé.")
        return False
    except Exception as e:
        print(f"❌ Exception inattendue lors de la conversion en PDF : {e}")
        return False

# --- SCRIPT PRINCIPAL ---
def main():
    """Fonction principale du script."""
    print("🚀 Démarrage du script d'observation des stablecoins EUR...")
    # ... (messages initiaux identiques) ...
    print(f"Date actuelle: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"ℹ️  Rappel: Les règles MiCA pour les stablecoins (ARTs & EMTs) sont applicables depuis le 30 juin 2024.")
    print(f"ℹ️  Veuillez vérifier et mettre à jour la section 'MICA_STATUS' dans le script avec les informations les plus récentes.\n")

    api_data = fetch_coin_data(API_KEY, SYMBOLS)
    if not api_data:
        print("❌ Arrêt du script en raison d'une erreur API.")
        return

    df_current, alertes = process_data(api_data, SYMBOLS, EMETTEURS, MICA_STATUS)

    cols_display_order = ["Nom", "Émetteur", "Conformité MiCA"] + \
                         [col for col in df_current.columns if col not in ["Nom", "Émetteur", "Conformité MiCA"]]
    df_display_final = df_current[cols_display_order].sort_values(by="Volume hebdo / Cap (%)", ascending=False).reset_index(drop=True)
    print("ℹ️  Le tableau principal dans la feuille 'Données' est trié par 'Volume hebdo / Cap (%)' (décroissant).")

    history_file_path = Path(HISTORY_FILENAME)
    historique_df = update_history(df_current, SYMBOLS, history_file_path)
    print(f"📈 Historique mis à jour dans '{history_file_path}'.")

    excel_file_path = Path(EXCEL_FILENAME)
    try:
        create_excel_report(df_display_final, historique_df, SYMBOLS, excel_file_path)
        print(f"✅ Rapport Excel généré : '{excel_file_path}'")

        # Tenter la conversion en PDF
        if export_excel_to_pdf_libreoffice(excel_file_path, excel_file_path.parent):
            # Optionnel: ouvrir le PDF au lieu de/en plus de l'Excel
            pdf_file_path = excel_file_path.with_suffix(".pdf")
            try:
                if platform.system() == "Darwin": os.system(f'open "{pdf_file_path}"')
                elif platform.system() == "Windows": os.startfile(str(pdf_file_path))
                elif platform.system() == "Linux": os.system(f'xdg-open "{pdf_file_path}"')
                print(f"📂 Tentative d'ouverture de '{pdf_file_path}'.")
            except Exception as e_open:
                print(f"ℹ️  Impossible d'ouvrir automatiquement le fichier PDF : {e_open}")
        else:
            # Si la conversion PDF échoue, ouvrir l'Excel comme avant
            try:
                if platform.system() == "Darwin": os.system(f'open "{excel_file_path}"')
                elif platform.system() == "Windows": os.startfile(str(excel_file_path))
                elif platform.system() == "Linux": os.system(f'xdg-open "{excel_file_path}"')
                print(f"📂 Tentative d'ouverture de '{excel_file_path}'.")
            except Exception as e_open_xls:
                 print(f"ℹ️  Impossible d'ouvrir automatiquement le fichier Excel : {e_open_xls}")


    except Exception as e:
        print(f"❌ Erreur lors de la création du rapport Excel ou de la conversion PDF : {e}")
        import traceback
        traceback.print_exc()
        return

    if alertes:
        print("\n⚠️ Alertes détectées :")
        for alerte in alertes:
            print(alerte)
    else:
        print("👍 Aucune alerte de capitalisation nulle détectée.")
    # L'ouverture du fichier est maintenant gérée dans le bloc try/except de create_excel_report

if __name__ == "__main__":
    main()





