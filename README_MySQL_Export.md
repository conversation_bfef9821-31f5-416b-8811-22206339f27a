# Export MySQL des Stablecoins EUR

## 📋 Description

Ce script Python modifié génère maintenant un export MySQL complet des données de stablecoins EUR récupérées depuis l'API CoinMarketCap. L'export contient le maximum de données structurées pour une utilisation en base de données.

## 🗄️ Contenu de l'export MySQL

### 1. Table principale : `stablecoins_eur`
- **Structure optimisée** avec index pour les performances
- **Données actuelles** de tous les stablecoins EUR
- **Colonnes** :
  - `id` : Clé primaire auto-incrémentée
  - `nom` : Symbole du stablecoin (ex: EURC, EURS)
  - `emetteur` : Société émettrice
  - `conformite_mica` : Statut de conformité MiCA
  - `prix_usd` : Prix en USD (6 décimales)
  - `capitalisation_usd` : Capitalisation de marché
  - `offre_circulation` : Tokens en circulation
  - `volume_24h_usd` : Volume 24h
  - `volume_hebdo_usd` : Volume hebdomadaire estimé
  - `volume_hebdo_cap_ratio` : Ratio volume/capitalisation (%)
  - `date_maj` : Timestamp de mise à jour

### 2. Table d'historique : `stablecoins_eur_historique`
- **Suivi temporel** des évolutions
- **Contrainte unique** par nom et date
- **Colonnes supplémentaires** :
  - `date_snapshot` : Date de la capture
  - `heure_snapshot` : Heure de la capture
  - `date_creation` : Timestamp de création

### 3. Procédure stockée : `InsererHistorique()`
- **Automatise** l'insertion des données actuelles dans l'historique
- **Gère les doublons** avec ON DUPLICATE KEY UPDATE
- **Usage** : `CALL InsererHistorique();`

## 🚀 Installation et utilisation

### Prérequis
- Serveur MySQL 5.7+ ou MariaDB 10.2+
- Accès administrateur à la base de données

### Étapes d'installation

1. **Créer la base de données** :
```sql
CREATE DATABASE stablecoins_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE stablecoins_db;
```

2. **Importer le fichier SQL** :
```bash
# Via ligne de commande
mysql -u username -p stablecoins_db < stablecoins_eur_data.sql

# Ou via MySQL client
SOURCE /chemin/vers/stablecoins_eur_data.sql;
```

3. **Vérifier l'installation** :
```sql
SHOW TABLES;
SELECT COUNT(*) FROM stablecoins_eur;
```

## 📊 Requêtes d'analyse incluses

Le fichier SQL contient des requêtes prêtes à l'emploi :

### Top 10 par capitalisation
```sql
SELECT nom, emetteur, capitalisation_usd, conformite_mica
FROM stablecoins_eur
ORDER BY capitalisation_usd DESC
LIMIT 10;
```

### Statistiques globales
```sql
SELECT 
    COUNT(*) as nombre_stablecoins,
    SUM(capitalisation_usd) as capitalisation_totale,
    AVG(capitalisation_usd) as capitalisation_moyenne,
    SUM(volume_24h_usd) as volume_24h_total,
    AVG(volume_hebdo_cap_ratio) as ratio_moyen
FROM stablecoins_eur;
```

### Répartition par émetteur
```sql
SELECT 
    emetteur,
    COUNT(*) as nombre_tokens,
    SUM(capitalisation_usd) as capitalisation_totale,
    AVG(prix_usd) as prix_moyen
FROM stablecoins_eur
GROUP BY emetteur
ORDER BY capitalisation_totale DESC;
```

## 🔄 Automatisation de l'historique

### Mise à jour quotidienne automatique
Pour automatiser la collecte d'historique, créez un événement MySQL :

```sql
DELIMITER //
CREATE EVENT IF NOT EXISTS daily_stablecoin_snapshot
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_DATE + INTERVAL 1 DAY
DO
BEGIN
    CALL InsererHistorique();
END //
DELIMITER ;

-- Activer le planificateur d'événements
SET GLOBAL event_scheduler = ON;
```

### Script de mise à jour
Combinez avec un cron job pour exécuter le script Python quotidiennement :

```bash
# Crontab : tous les jours à 9h00
0 9 * * * cd /chemin/vers/script && python3 scrapeurohebdo.py && mysql -u user -p stablecoins_db -e "CALL InsererHistorique();"
```

## 📈 Analyse des tendances

### Évolution sur 30 jours
```sql
SELECT 
    nom,
    date_snapshot,
    capitalisation_usd,
    LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot) as cap_precedente,
    ROUND(((capitalisation_usd - LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot)) / LAG(capitalisation_usd) OVER (PARTITION BY nom ORDER BY date_snapshot)) * 100, 2) as variation_pct
FROM stablecoins_eur_historique
WHERE date_snapshot >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
ORDER BY nom, date_snapshot;
```

### Volatilité par stablecoin
```sql
SELECT 
    nom,
    COUNT(*) as nb_mesures,
    MIN(prix_usd) as prix_min,
    MAX(prix_usd) as prix_max,
    AVG(prix_usd) as prix_moyen,
    STDDEV(prix_usd) as volatilite
FROM stablecoins_eur_historique
WHERE date_snapshot >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY nom
ORDER BY volatilite DESC;
```

## 🔧 Personnalisation

### Ajouter des colonnes
Pour enrichir les données, modifiez la fonction `create_mysql_export()` dans le script Python :

```python
# Exemple : ajouter une colonne pour la blockchain
f.write("    blockchain VARCHAR(50) DEFAULT 'Ethereum',\n")
```

### Modifier les types de données
Ajustez les types selon vos besoins :
- `DECIMAL(20,2)` pour plus de précision sur les montants
- `VARCHAR(200)` pour des noms d'émetteurs plus longs
- `JSON` pour stocker des métadonnées supplémentaires

## 📁 Fichiers générés

- `stablecoins_eur_data.sql` : Export MySQL complet
- `Rapport_Stablecoins_EUR.xlsx` : Rapport Excel (inchangé)
- `Rapport_Stablecoins_EUR.pdf` : Version PDF (inchangée)

## 🎯 Avantages de l'export MySQL

✅ **Performance** : Index optimisés pour les requêtes rapides  
✅ **Historique** : Suivi temporel automatisé  
✅ **Analyse** : Requêtes SQL puissantes  
✅ **Intégration** : Compatible avec tous les outils BI  
✅ **Scalabilité** : Gestion de gros volumes de données  
✅ **Automatisation** : Procédures stockées et événements  

## 🔍 Dépannage

### Erreurs courantes

1. **Charset non supporté** :
```sql
ALTER DATABASE stablecoins_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. **Privilèges insuffisants** :
```sql
GRANT ALL PRIVILEGES ON stablecoins_db.* TO 'username'@'localhost';
FLUSH PRIVILEGES;
```

3. **Événements désactivés** :
```sql
SET GLOBAL event_scheduler = ON;
```

---

**Note** : Ce README accompagne la version modifiée du script `scrapeurohebdo.py` qui génère maintenant un export MySQL complet en plus des rapports Excel et PDF existants.
