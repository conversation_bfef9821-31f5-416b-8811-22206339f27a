import requests
import pandas as pd

COINGECKO_API_URL = "https://api.coingecko.com/api/v3/coins/markets"

euro_stablecoins = {
    "euro-coin": ("EURC", "Circle"),
    "stasis-eurs": ("EURS", "Stasis"),
    "tether-eurt": ("EURT", "Tether"),
    "ageur": ("agEUR", "Angle Protocol"),
    "par-stablecoin": ("PAR", "Mimo Capital"),
    "lugh": ("EURL", "Lugh / Casino Group"),
    "monerium-eur": ("EURE", "Monerium"),
    "eur-anchored-coin": ("AEUR", "Anchored Coins"),
    "celo-euro": ("CEUR", "Celo Foundation"),
    "stablr-euro": ("EURR", "StablR"),
    "quantoz-eurq": ("EURQ", "Quantoz"),
    "vnx-euro": ("VEUR", "VNX"),
    "decentralized-euro": ("DEURO", "Decentralized Euros"),
    "seur": ("SEUR", "Synthetix / Jarvis"),
    "euroe-stablecoin": ("EUROE", "Membrane Finance"),
    "e-money-eur": ("EEUR", "e-Money"),
}

params = {
    'vs_currency': 'usd',
    'ids': ','.join(euro_stablecoins.keys()),
    'order': 'market_cap_desc',
    'per_page': 50,
    'page': 1,
    'price_change_percentage': '24h,7d'
}

response = requests.get(COINGECKO_API_URL, params=params)
market_data = response.json()

records = []

for coin in market_data:
    code, issuer = euro_stablecoins[coin["id"]]
    records.append({
        "Nom": code,
        "Émetteur": issuer,
        "Prix (USD)": round(coin["current_price"], 4),
        "Capitalisation (USD)": round(coin["market_cap"], 2) if coin["market_cap"] else None,
        "Offre en circulation": round(coin["circulating_supply"], 2) if coin["circulating_supply"] else None,
        "Volume 24h (USD)": round(coin["total_volume"], 2) if coin["total_volume"] else None,
        "Variation 24h (%)": round(coin["price_change_percentage_24h"], 2) if coin["price_change_percentage_24h"] else None,
        "Variation 7j (%)": round(coin["price_change_percentage_7d_in_currency"], 2) if coin["price_change_percentage_7d_in_currency"] else None,
        "Source": "CoinGecko"
    })

manual_data = [
    {"Nom": "EURCV", "Émetteur": "Société Générale FORGE", "Prix (USD)": 1.13, "Capitalisation (USD)": 46963705,
     "Offre en circulation": None, "Volume 24h (USD)": None, "Variation 24h (%)": None,
     "Variation 7j (%)": None, "Source": "Manuel"},
    {"Nom": "EURA", "Émetteur": "Inconnu", "Prix (USD)": 1.14, "Capitalisation (USD)": 21924690,
     "Offre en circulation": None, "Volume 24h (USD)": None, "Variation 24h (%)": None,
     "Variation 7j (%)": None, "Source": "Manuel"},
    {"Nom": "EUROP", "Émetteur": "Inconnu", "Prix (USD)": 1.13, "Capitalisation (USD)": None,
     "Offre en circulation": None, "Volume 24h (USD)": None, "Variation 24h (%)": None,
     "Variation 7j (%)": None, "Source": "Manuel"},
    {"Nom": "EURI", "Émetteur": "Inconnu", "Prix (USD)": 1.13, "Capitalisation (USD)": None,
     "Offre en circulation": None, "Volume 24h (USD)": None, "Variation 24h (%)": None,
     "Variation 7j (%)": None, "Source": "Manuel"},
    {"Nom": "EUROT", "Émetteur": "Token Teknoloji", "Prix (USD)": 1.13, "Capitalisation (USD)": None,
     "Offre en circulation": None, "Volume 24h (USD)": None, "Variation 24h (%)": None,
     "Variation 7j (%)": None, "Source": "Manuel"},
    {"Nom": "JEUR", "Émetteur": "Jarvis Network", "Prix (USD)": 0.6924, "Capitalisation (USD)": None,
     "Offre en circulation": None, "Volume 24h (USD)": None, "Variation 24h (%)": None,
     "Variation 7j (%)": None, "Source": "Manuel"},
]

records.extend(manual_data)

df = pd.DataFrame(records)

df = df.sort_values(by="Capitalisation (USD)", ascending=False)

df["Prix (USD)"] = df["Prix (USD)"].round(4)
df["Capitalisation (USD)"] = df["Capitalisation (USD)"].apply(lambda x: f"{x:,.2f}" if pd.notnull(x) else "N/A")
df["Offre en circulation"] = df["Offre en circulation"].apply(lambda x: f"{x:,.2f}" if pd.notnull(x) else "N/A")
df["Volume 24h (USD)"] = df["Volume 24h (USD)"].apply(lambda x: f"{x:,.2f}" if pd.notnull(x) else "N/A")

csv_file = "stablecoins_euro_data_complet.csv"
excel_file = "stablecoins_euro_data_complet.xlsx"

df.to_csv(csv_file, index=False)
df.to_excel(excel_file, index=False)

print(f"✅ CSV généré : {csv_file}")
print(f"✅ Excel généré : {excel_file}")

import os
import platform

# Ouvrir le fichier Excel automatiquement après création
if platform.system() == "Darwin":  # macOS
    os.system(f'open "{excel_file}"')
elif platform.system() == "Windows":
    os.startfile(excel_file)
elif platform.system() == "Linux":
    os.system(f'xdg-open "{excel_file}"')